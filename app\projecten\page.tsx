import Image from "next/image"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowRight } from "lucide-react"
import type { Metadata } from "next"

import { AnimatedText } from "@/components/motion/animated-text"
import { AnimatedButton } from "@/components/motion/animated-button"
import { AnimatedCard } from "@/components/motion/animated-card"

import Testimonials from "@/components/testimonials"

export const metadata: Metadata = {
  title: "Projecten | Dakrenovatie Referenties & Dakdoorvoer Cases | Tappel Dakwerken",
  description: "Tappel Dakwerken projecten: 400+ uitgevoerde dakrenovaties, dakdoorvoer referenties en montageanker cases in Rotterdam, Amsterdam en Utrecht. Bekijk onze resultaten en referenties.",
  keywords: "dakrenovatie projecten, dakdoorvoer referenties, montageanker cases, plat dak renovatie voorbeelden, EPDM projecten, bitumen dak cases, zonnepanelen dakdoorvoeren",
  authors: [{ name: "<PERSON><PERSON> Dakwerken" }],
  creator: "Tappel Dakwerken",
  publisher: "Tappel Dakwerken",
  alternates: {
    canonical: "/projecten",
  },
  openGraph: {
    title: "Projecten | Tappel Dakwerken | Dakrenovatie Referenties & Dakdoorvoer Cases",
    description: "Tappel Dakwerken projecten ✓ 400+ uitgevoerde dakrenovaties ✓ Dakdoorvoer referenties ✓ Montageanker cases ✓ Rotterdam, Amsterdam, Utrecht",
    url: "https://tappeldakwerken.nl/projecten",
    siteName: "Tappel Dakwerken",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Tappel Dakwerken Projecten - Dakrenovatie Referenties",
      },
    ],
    locale: "nl_NL",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Projecten | Tappel Dakwerken",
    description: "Tappel Dakwerken projecten: 400+ uitgevoerde dakrenovaties, dakdoorvoer referenties en montageanker cases. Bekijk onze resultaten.",
    images: ["/og-image.jpg"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  category: "Dakwerken Projecten",
}

// Project data
const projects = [
  {
    id: "solarNRG-landelijk",
    title: "Dakdoorvoeren voor SolarNRG",
    summary: "Grootschalige landelijke installatie van 250+ dakdoorvoeren voor PV-projecten in samenwerking met SolarNRG, Vereniging Eigen Huis en Rabobank.",
    category: "Dakdoorvoeren",
    location: "Landelijk",
    image: "/images/projecten/solarnrg/landelijk-dakdoorvoeren-solarnrg-foto8.jpg",
    alt: "Dakdoorvoeren landelijk geplaatst voor SolarNRG, Vereniging Eigen Huis en Rabobank door Tappel Dakwerken",
    description: "Tappel Dakwerken plaatste meer dan 250 dakdoorvoeren landelijk voor PV-projecten van SolarNRG, in opdracht van Vereniging Eigen Huis en Rabobank.",
  },
  {
    id: "dakrenovatie-montageankers-zoeterwoude",
    title: "Dakrenovatie en Montageankers Museum De Bommelzolder",
    summary: "Tappel Dakwerken vernieuwde het schuine bitumen dak van museum De Bommelzolder in Zoeterwoude en plaatste 32 montageankers en nieuwe dakdoorvoeren voor de komst van een zonnepaneleninstallatie.",
    category: "Dakrenovaties",
    location: "Zoeterwoude",
    image: "/images/projecten/zoeterwoude-bommelzolder/zoeterwoude-dakrenovatie-bommelzolder-foto8.jpg",
    alt: "Dakrenovatie en montageankers museum De Bommelzolder Zoeterwoude door Tappel Dakwerken",
    description: "Tappel Dakwerken vernieuwde het schuine bitumen dak van museum De Bommelzolder en plaatste 32 montageankers voor zonnepanelen.",
  },
  {
    id: "dakrenovatie-pv-demontage-zoetermeer",
    title: "Dakrenovatie + PV Demontage Zoetermeer",
    summary: "Complete dakrenovatie in Zoetermeer inclusief professionele demontage en hermontage van zonnepanelensysteem.",
    category: "Dakrenovatie",
    location: "Zoetermeer",
    image: "/images/projecten/zoetermeer/zoetermeer-dakrenovatie-rokkeveen-foto8.jpg",
    alt: "Complete dakrenovatie met PV demontage en hermontage in Zoetermeer Rokkeveen door Tappel Dakwerken",
  },
  {
    id: "dakrenovatie-den-haag",
    title: "Dakrenovatie met Zonnepanelen Den Haag",
    summary: "Dak laten renoveren en meteen de zonnepanelen laten verwijderen en terugplaatsen? In Den Haag pakte Tappel Dakwerken dit compleet aan, inclusief een nieuwe dakdoorvoer.",
    category: "Dakrenovatie",
    location: "Den Haag",
    image: "/images/projecten/dakrenovatie-zonnepanelen-dakdoorvoer-den-haag.jpg",
    alt: "Tappel Dakwerken voerde 2 dakrenovaties uit inclusief verwijderen en terugplaatsen zonnepanelen en plaatsen dakdoorvoer in Den Haag",
    description: "In Den Haag pakte Tappel Dakwerken een complete dakrenovatie aan, inclusief het verwijderen en terugplaatsen van zonnepanelen en het installeren van een nieuwe dakdoorvoer.",
  },
  {
    id: "dakinspectie-onderhoud-eindhoven",
    title: "Dakinspectie en Preventief Onderhoud Eindhoven",
    summary: "Uitgebreide dakinspectie en preventief onderhoud voor een kantoorgebouw in Eindhoven.",
    category: "Dakinspectie",
    location: "Eindhoven",
    image: "/placeholder.svg?height=400&width=600",
    alt: "Dakinspectie en preventief onderhoud van een kantoorgebouw in Eindhoven",
  },
  {
    id: "groendak-installatie-groningen",
    title: "Groendak Installatie Groningen",
    summary: "Aanleg van een duurzaam groendak op een nieuwbouwwoning in Groningen.",
    category: "Groendak",
    location: "Groningen",
    image: "/placeholder.svg?height=400&width=600",
    alt: "Installatie van een groendak op een nieuwbouwwoning in Groningen",
  },
]

export default function ProjectenPage() {
  return (
    <>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-gray-50 to-white py-12 md:py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <AnimatedText variant="fade-up">
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                Onze <span className="text-tappel-orange">Projecten</span>
              </h1>
            </AnimatedText>
            <AnimatedText variant="fade-up" delay={0.1}>
              <p className="text-xl text-gray-700">
                Bekijk een selectie van onze recente projecten en ontdek hoe wij klanten hebben geholpen met duurzame en betrouwbare dakoplossingen. Van grootschalige dakdoorvoer installaties tot complete dakrenovaties met zonnepanelen - ontdek onze meest indrukwekkende projecten. Met meer dan 400 uitgevoerde projecten, landelijke dekking en specialistische technieken staan wij garant voor vakkundig werk.
              </p>
            </AnimatedText>
          </div>
        </div>
      </section>



      {/* Projects Grid */}
      <section className="py-16 md:py-24 bg-gradient-to-br from-gray-50 to-white">
        <div className="container mx-auto px-4">
          <AnimatedText variant="fade-up">
            <h2 className="text-3xl font-bold text-center mb-12">
              Uitgelichte <span className="text-tappel-orange">Projecten</span>
            </h2>
          </AnimatedText>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {projects.map((project, index) => (
              <AnimatedText key={project.id} variant="fade-up" delay={Math.min(0.05 + index * 0.02, 0.2)}>
                <Link href={`/projecten/${project.id}`} className="block group">
                  <AnimatedCard variant="hover-lift" className="bg-white rounded-2xl overflow-hidden shadow-lg border border-gray-100 flex flex-col h-full group-hover:shadow-xl transition-all duration-300 hover:border-tappel-orange/20">
                      <div className="relative h-64 flex-shrink-0">
                        <Image src={project.image || "/placeholder.svg"} alt={project.alt} fill className="object-cover group-hover:scale-105 transition-transform duration-300" />
                        <div className="absolute top-4 left-4 bg-tappel-orange text-white text-sm font-medium py-1 px-3 rounded-full">
                          {project.category}
                        </div>
                        <div className="absolute top-4 right-4 bg-white text-gray-800 text-sm font-medium py-1 px-3 rounded-full">
                          {project.location}
                        </div>
                      </div>
                      <div className="p-6 flex flex-col flex-grow">
                        <h2 className="text-xl font-bold mb-3 line-clamp-2 group-hover:text-tappel-orange transition-colors duration-300">{project.title}</h2>
                        <p className="text-gray-600 mb-6 flex-grow line-clamp-3">{project.summary}</p>
                        <div className="text-tappel-orange font-medium flex items-center mt-auto">
                          Bekijk Project{" "}
                          <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                        </div>
                      </div>
                    </AnimatedCard>
                </Link>
              </AnimatedText>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 md:py-24 bg-gradient-to-r from-tappel-orange to-orange-400 text-white">
        <div className="container mx-auto px-4 text-center">
          <AnimatedText variant="fade-up">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">Klaar voor uw eigen dakproject?</h2>
          </AnimatedText>
          <AnimatedText variant="fade-up" delay={0.1}>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Neem contact met ons op voor een vrijblijvende offerte of adviesgesprek. Wij denken graag met u mee over de
              beste oplossing voor uw situatie.
            </p>
          </AnimatedText>
          <AnimatedText variant="fade-up" delay={0.15}>
            <AnimatedButton
              asChild
              size="lg"
              variant="secondary"
              className="bg-white text-tappel-orange hover:bg-gray-100 font-bold"
              glow
            >
              <Link href="/contact">OFFERTE AANVRAGEN</Link>
            </AnimatedButton>
          </AnimatedText>
        </div>
      </section>

      {/* Testimonials */}
      <Testimonials />
    </>
  )
}
