import Image from "next/image"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { AnimatedButton } from "@/components/motion/animated-button"
import { AnimatedText, AnimatedTextStagger } from "@/components/motion/animated-text"
import { AnimatedCard, ServiceCard, ProjectCard } from "@/components/motion/animated-card"
import { CardStack } from "@/components/motion/card-stack"
import { CheckCircle, ArrowRight, Shield, PenToolIcon as Tool, Clock, Award, Phone } from "lucide-react"
import BlogPreview from "@/components/blog-preview"
import Testimonials from "@/components/testimonials"
import type { Metadata } from "next"

export const metadata: Metadata = {
  title: "Tappel Dakwerken | Dakrenovatie Specialist Randstad | EPDM & Bitumen Daken",
  description: "Dakrenovatie specialist Tappel Dakwerken. Platte daken renoveren door Noord-Holland, Zuid-Holland en Utrecht. EPDM en bitumen dakbedekking met 10 jaar garantie. Bel 06-21452819 voor gratis offerte.",
  keywords: "dakrenovatie specialist, plat dak renovatie, <PERSON><PERSON> dak, bitumen dak, dakdekker Rotterdam, dakdekker Amsterdam, dakdekker Utrecht, dakdoorvoeren, montageankers, dakisolatie, lekkage herstel, groendak",
  authors: [{ name: "Tappel Dakwerken" }],
  creator: "Tappel Dakwerken",
  publisher: "Tappel Dakwerken",
  alternates: {
    canonical: "/",
  },
  openGraph: {
    title: "Tappel Dakwerken | Dakrenovatie Specialist Randstad | EPDM & Bitumen Daken",
    description: "Dakrenovatie specialist voor platte daken in de Randstad. EPDM en bitumen dakbedekking met 10 jaar garantie. 400+ tevreden klanten door Noord-Holland, Zuid-Holland en Utrecht.",
    url: "https://tappeldakwerken.nl",
    siteName: "Tappel Dakwerken",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Tappel Dakwerken - Dakrenovatie Specialist Platte Daken Randstad",
      },
    ],
    locale: "nl_NL",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Tappel Dakwerken | Dakrenovatie Specialist Randstad",
    description: "Dakrenovatie specialist voor platte daken. EPDM en bitumen dakbedekking met 10 jaar garantie. Actief door Noord-Holland, Zuid-Holland en Utrecht.",
    images: ["/og-image.jpg"],
    creator: "@tappeldakwerken",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  category: "Dakwerken",
  classification: "Dakrenovatie Specialist",
}

// Featured projects for homepage
const featuredProjects = [
  {
    id: "solarNRG-landelijk",
    title: "Dakdoorvoeren voor SolarNRG",
    summary: "Grootschalige landelijke installatie van 250+ dakdoorvoeren voor PV-projecten in samenwerking met SolarNRG, Vereniging Eigen Huis en Rabobank.",
    image: "/images/projecten/solarnrg/landelijk-dakdoorvoeren-solarnrg-foto8.jpg",
    alt: "Dakdoorvoeren landelijk geplaatst voor SolarNRG, Vereniging Eigen Huis en Rabobank door Tappel Dakwerken",
    description: "Tappel Dakwerken plaatste meer dan 250 dakdoorvoeren landelijk voor PV-projecten van SolarNRG, in opdracht van Vereniging Eigen Huis en Rabobank.",
  },
  {
    id: "dakrenovatie-montageankers-zoeterwoude",
    title: "Dakrenovatie en Montageankers Museum De Bommelzolder",
    summary: "Tappel Dakwerken vernieuwde het schuine bitumen dak van museum De Bommelzolder in Zoeterwoude en plaatste 32 montageankers en nieuwe dakdoorvoeren voor de komst van een zonnepaneleninstallatie.",
    image: "/images/projecten/zoeterwoude-bommelzolder/zoeterwoude-dakrenovatie-bommelzolder-foto8.jpg",
    alt: "Dakrenovatie en montageankers museum De Bommelzolder Zoeterwoude door Tappel Dakwerken",
    description: "Tappel Dakwerken vernieuwde het schuine bitumen dak van museum De Bommelzolder en plaatste 32 montageankers voor zonnepanelen.",
  },
  {
    id: "dakrenovatie-pv-demontage-zoetermeer",
    title: "Dakrenovatie + PV Demontage Zoetermeer",
    summary: "Na lekkage bij een PV-installatie in Zoetermeer, schakelde de klant Tappel Dakwerken in voor een complete dakrenovatie inclusief het verwijderen en terugplaatsen van het zonnestroomsysteem.",
    image: "/images/projecten/zoetermeer/zoetermeer-dakrenovatie-rokkeveen-foto8.jpg",
    alt: "Dakrenovatie en PV demontage in Zoetermeer door Tappel Dakwerken",
    description: "Tappel Dakwerken voerde een complete dakrenovatie uit in Zoetermeer, inclusief het verwijderen en terugplaatsen van een PV-systeem.",
  },
]

export default function Home() {
  return (
    <>
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-[url('/images/homepage/dakdoorvoer-plaatsen-plat-dak-tappel-dakwerken.jpg')] bg-cover bg-center" style={{minHeight: '80vh'}}>
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="container mx-auto px-4 py-16 md:py-24 lg:py-32 relative z-10">
          <div className="grid md:grid-cols-2 gap-8 items-center">
            <div className="text-white space-y-6">
              <AnimatedText variant="fade-up" delay={0.1}>
                <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
                  Wij maken het zichtbaar beter
                </h1>
              </AnimatedText>
              <AnimatedText variant="fade-up" delay={0.2}>
                <p className="text-xl md:text-2xl font-medium">Specialist in duurzame dakrenovaties voor platte en licht hellende daken</p>
              </AnimatedText>
              <AnimatedTextStagger variant="fade-up" stagger={0.1} delay={0.3} className="space-y-4">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-6 w-6 flex-shrink-0" />
                  <span>Ook met bestaande zonnepanelen installaties</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-6 w-6 flex-shrink-0" />
                  <span>Éen aanspreekpunt</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-6 w-6 flex-shrink-0" />
                  <span>Zichtbaar resultaat</span>
                </div>
              </AnimatedTextStagger>
              <div className="pt-4">
                <AnimatedButton
                  asChild
                  size="xl"
                  variant="secondary"
                  glow
                  className="font-bold"
                >
                  <Link href="/contact">AFSPRAAK MAKEN</Link>
                </AnimatedButton>
              </div>
            </div>
            <div className="relative">
              {/* Image removed - now used as background */}
            </div>
          </div>
        </div>
      </section>

      {/* Intro Section */}
      <section className="py-16 md:py-12 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center mb-12">
            <AnimatedText variant="fade-up">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">Duurzaam Dakwerk</h2>
            </AnimatedText>
            <AnimatedText variant="fade-up" delay={0.2}>
              <p className="text-lg text-gray-700 leading-relaxed">
                Tappel Dakwerken is gespecialiseerd in verduurzaming en renovatie van platte en licht hellende daken. Wij combineren traditioneel vakmanschap met een moderne aanpak zoals isoleren, overlagen en verduurzamen. Bij een bestaand PV-systeem nemen wij de complete demontage en herinstallatie voor onze rekening, zodat de waterdichtheid gewaarborgd blijft.
              </p>
            </AnimatedText>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <ServiceCard
              icon={<Shield className="h-6 w-6 text-white" />}
              title="Betrouwbare Oplossingen"
              description="Onze dienstverlening richt zich op het bieden van duurzame en betrouwbare oplossing, specifiek afgestemd op de behoefte van de klant."
            />

            <ServiceCard
              icon={<Tool className="h-6 w-6 text-white" />}
              title="Vakkundig Uitgevoerd"
              description="Ons team werkt volgens de geldende richtlijnen en levert strak, schoon en veilig op. U ontvangt helder advies vóór, tijdens en na de werkzaamheden."
            />

            <ServiceCard
              icon={<Clock className="h-6 w-6 text-white" />}
              title="Snelle Service"
              description="We begrijpen dat tijd kostbaar is. Daarom streven we naar een snelle en efficiënte service zonder in te leveren op kwaliteit."
            />
          </div>

          <div className="text-center mt-8">
            <Button 
              asChild 
              className="font-bold bg-rtdak-blue hover:bg-rtdak-blue/90 transition-all duration-300 hover:shadow-md"
            >
              <Link href="/contact">NEEM CONTACT OP</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Services Highlight */}
      <section className="py-16 md:py-12 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center mb-12">
            <AnimatedText variant="fade-up">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">Onze Diensten</h2>
            </AnimatedText>
            <AnimatedText variant="fade-up" delay={0.2}>
              <p className="text-lg text-gray-700">
                Ontdek onze kernactiviteiten voor particuliere en zakelijke klanten.
              </p>
            </AnimatedText>
          </div>

          <div className="grid md:grid-cols-3 gap-8 mb-12">
            <Link href="/diensten#dakrenovaties" className="group">
              <AnimatedCard variant="hover-lift" className="bg-white rounded-lg overflow-hidden shadow-md h-full">
                  <div className="relative h-64 overflow-hidden">
                    <Image
                      src="/images/diensten/dakrenovatie-plat-dak-tappel-dakwerken.jpg"
                      alt="Dakrenovaties met hoogwaardige materialen en oog voor detail door Tappel Dakwerken"
                      fill
                      className="object-cover transition-transform duration-500 group-hover:scale-110"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </div>
                  <div className="p-6">
                    <h3 className="text-2xl font-bold mb-3 text-gray-900 group-hover:text-tappel-orange transition-colors">
                      Dakrenovaties
                    </h3>
                    <p className="text-gray-600 mb-4">
                      Is uw dak aan vernieuwing toe? Wij renoveren met hoogwaardige materialen en oog voor detail. Overlagen of compleet vervangen gebeurt na een duidelijke analyse en met advies over isolatie.
                    </p>
                    <div className="text-tappel-orange font-medium group-hover:underline">
                      Meer informatie →
                    </div>
                  </div>
                </AnimatedCard>
            </Link>

            <Link href="/diensten#dakinspecties" className="group">
              <AnimatedCard variant="hover-lift" className="bg-white rounded-lg overflow-hidden shadow-md h-full">
                  <div className="relative h-64 overflow-hidden">
                    <Image
                      src="/images/diensten/dakinspectie-lekkage-analyse-tappel-dakwerken.jpg"
                      alt="Grondige dakinspecties met duidelijk rapport en praktisch plan door Tappel Dakwerken"
                      fill
                      className="object-cover transition-transform duration-500 group-hover:scale-110"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </div>
                  <div className="p-6">
                    <h3 className="text-2xl font-bold mb-3 text-gray-900 group-hover:text-tappel-blue transition-colors">
                      Dakinspecties
                    </h3>
                    <p className="text-gray-600 mb-4">
                      Met een grondige inspectie brengen wij de staat van bedekking, details en afvoeren in kaart. U ontvangt een duidelijk rapport met prioriteiten en een praktisch plan voor herstel of renovatie.
                    </p>
                    <div className="text-tappel-blue font-medium group-hover:underline">
                      Meer informatie →
                    </div>
                  </div>
                </AnimatedCard>
            </Link>

            <Link href="/diensten#lekkages" className="group">
              <AnimatedCard variant="hover-lift" className="bg-white rounded-lg overflow-hidden shadow-md h-full">
                  <div className="relative h-64 overflow-hidden">
                    <Image
                      src="/images/diensten/lekkage-opsporen-tappel-dakwerken.jpg"
                      alt="Snelle en nauwkeurige lekkage opsporing en herstel door Tappel Dakwerken"
                      fill
                      className="object-cover transition-transform duration-500 group-hover:scale-110"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </div>
                  <div className="p-6">
                    <h3 className="text-2xl font-bold mb-3 text-gray-900 group-hover:text-green-600 transition-colors">
                      Lekkage Herstel
                    </h3>
                    <p className="text-gray-600 mb-4">
                      Wij sporen de oorzaak van de lekkage snel en nauwkeurig op en herstellen deze vakkundig. Zo voorkomt u vervolgschade en is het dak weer betrouwbaar in gebruik.
                    </p>
                    <div className="text-green-600 font-medium group-hover:underline">
                      Meer informatie →
                    </div>
                  </div>
                </AnimatedCard>
            </Link>
          </div>

          <div className="text-center mt-8">
            <AnimatedButton
              asChild
              variant="primary"
              size="lg"
              className="font-bold"
            >
              <Link href="/diensten">BEKIJK ONZE DIENSTEN</Link>
            </AnimatedButton>
          </div>
        </div>
      </section>

      {/* Featured Projects Section */}
      <section className="py-16 md:py-12 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-12 items-center mb-8">
              <div>
                <AnimatedText variant="fade-up">
                  <h2 className="text-3xl md:text-4xl font-bold mb-6">Recente Projecten</h2>
                </AnimatedText>
                <AnimatedText variant="fade-up" delay={0.2}>
                  <p className="text-lg text-gray-700 mb-8">
                    Van grootschalige dakdoorvoer installaties tot complete dakrenovaties -
                    ontdek onze meest recente projecten. Hover over de kaarten voor details.
                  </p>
                </AnimatedText>
                <AnimatedText variant="fade-up" delay={0.4}>
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-tappel-orange rounded-full"></div>
                      <span className="text-gray-700">Landelijke projecten</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-gray-700">Zonnepaneel integratie</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                      <span className="text-gray-700">Complete renovaties</span>
                    </div>
                  </div>
                </AnimatedText>

                <AnimatedText variant="fade-up" delay={0.6}>
                  <div className="mt-8 text-center lg:text-left">
                    <AnimatedButton
                      asChild
                      size="lg"
                      variant="primary"
                      className="font-medium"
                    >
                      <Link href="/projecten">BEKIJK ALLE PROJECTEN</Link>
                    </AnimatedButton>
                  </div>
                </AnimatedText>
              </div>

              <div className="flex justify-center lg:justify-end">
                <div className="w-full max-w-xl">
                  <CardStack
                    items={featuredProjects.map(project => ({
                      id: project.id,
                      title: project.title,
                      description: project.summary || "",
                      image: project.image,
                      location: "Nederland",
                      tags: ["Dakwerk"],
                      year: "2024",
                      href: `/projecten/${project.id}`
                    }))}
                    maxVisible={6}
                    stackOffset={20}
                    scaleStep={0.07}
                    className="w-full"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Blog Preview Section */}
      <BlogPreview />

      {/* Testimonials */}
      <Testimonials />

      {/* Werkgebied Section */}
      <section className="py-16 md:py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">Ons Werkgebied</h2>
              <p className="text-lg text-gray-700 max-w-3xl mx-auto">
                Tappel Dakwerken is actief in heel de Randstad. Van Amsterdam tot Rotterdam, van Utrecht tot Den Haag - wij renoveren platte daken in uw regio.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8 mb-12">
              <Link href="/dakrenovatie-zuid-holland">
                <AnimatedCard variant="hover-lift" className="p-6 group">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-xl font-bold text-gray-900 group-hover:text-tappel-orange transition-colors">
                      Zuid-Holland
                    </h3>
                    <ArrowRight className="h-5 w-5 text-gray-400 group-hover:text-tappel-orange transition-colors" />
                  </div>
                  <p className="text-gray-600 mb-4">
                    Dakrenovatie in Rotterdam, Den Haag, Zoetermeer, Leiden, Delft en omgeving.
                  </p>
                  <div className="text-sm text-tappel-orange font-medium flex items-center gap-2">
                    <div className="w-2 h-2 bg-tappel-orange rounded-full"></div>
                    Heel Zuid-Holland • 400+ projecten
                  </div>
                </AnimatedCard>
              </Link>

              <Link href="/dakrenovatie-noord-holland">
                <AnimatedCard variant="hover-lift" className="p-6 group">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-xl font-bold text-gray-900 group-hover:text-tappel-orange transition-colors">
                      Noord-Holland
                    </h3>
                    <ArrowRight className="h-5 w-5 text-gray-400 group-hover:text-tappel-orange transition-colors" />
                  </div>
                  <p className="text-gray-600 mb-4">
                    Dakrenovatie in Amsterdam, Haarlem, Amstelveen, Alkmaar en omgeving.
                  </p>
                  <div className="text-sm text-tappel-orange font-medium flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    Heel Noord-Holland • Groendak specialist
                  </div>
                </AnimatedCard>
              </Link>

              <Link href="/dakrenovatie-provincie-utrecht">
                <AnimatedCard variant="hover-lift" className="p-6 group">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-xl font-bold text-gray-900 group-hover:text-tappel-orange transition-colors">
                      Utrecht
                    </h3>
                    <ArrowRight className="h-5 w-5 text-gray-400 group-hover:text-tappel-orange transition-colors" />
                  </div>
                  <p className="text-gray-600 mb-4">
                    Dakrenovatie in Utrecht, Amersfoort, Nieuwegein, Zeist en omgeving.
                  </p>
                  <div className="text-sm text-tappel-orange font-medium flex items-center gap-2">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    Provincie Utrecht • Centrale ligging
                  </div>
                </AnimatedCard>
              </Link>
            </div>

            <div className="text-center">
              <p className="text-gray-600 mb-6">
                Staat uw plaats er niet bij? Geen probleem! Wij werken door heel Noord-Holland, Zuid-Holland en Utrecht, inclusief alle omliggende gemeenten en dorpen.
              </p>
              <AnimatedButton asChild variant="primary" glow>
                <Link href="/contact">VRAAG OFFERTE AAN</Link>
              </AnimatedButton>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 md:py-12 bg-tappel-orange text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">Klaar voor een duurzaam en betrouwbaar dak?</h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            Plan een dakinspectie of vraag direct een offerte aan voor dakrenovatie. We denken met je mee en leveren volgens afspraak.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <AnimatedButton
              asChild
              size="xl"
              variant="secondary"
              glow
            >
              <Link href="/contact">CONTACT OPNEMEN</Link>
            </AnimatedButton>
            <AnimatedButton
              asChild
              size="xl"
              variant="outline"
              className="bg-white text-tappel-orange border-white hover:bg-white hover:text-tappel-orange"
            >
              <Link href="/diensten">DIENSTEN BEKIJKEN</Link>
            </AnimatedButton>
          </div>
        </div>
      </section>
    </>
  )
}
