"use client"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Menu, X, ChevronDown } from "lucide-react"
import { MagneticNavItem, MagneticButton } from "@/components/motion/magnetic"
import { AnimatedBackground, NavItem } from "@/components/motion/animated-background"

const navigation = [
  { name: "HOME", href: "/", title: "Welkom bij Tappel Dakwerken" },
  {
    name: "DIENSTEN",
    href: "/diensten/",
    title: "Dak<PERSON>orvoer, renovatie & inspectie",
    dropdown: [
      {
        name: "On<PERSON> Diensten",
        items: [
          { name: "Dakdoorvoeren", href: "/diensten/dakdoorvoeren", description: "Professionele dakdoorvoer installatie" },
          { name: "Dakrenovaties", href: "/diensten/dakrenovaties", description: "Complete dakrenovatie en overlagen" },
          { name: "Montageankers", href: "/diensten/montageankers", description: "Zonnepaneel montageankers" },
          { name: "Dakinspect<PERSON>", href: "/diensten/dakinspecties", description: "Grondige dakinspectie en rapport" },
          { name: "Lekkage Herstel", href: "/diensten/lekkages", description: "Snelle lekkage opsporing en herstel" },
        ]
      }
    ]
  },
  { name: "PROJECTEN", href: "/projecten/", title: "Uitgevoerde dakwerken" },
  { name: "BLOG", href: "/blog/", title: "Dakwerk tips en nieuws" },
  { name: "OVER ONS", href: "/over-ons/", title: "Ervaren dakdekkersbedrijf" },
]

export default function Header() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [dropdownOpen, setDropdownOpen] = useState<string | null>(null)

  return (
    <header className="sticky top-0 z-50 px-2 py-4 md:px-4">
      <div className="max-w-7xl mx-auto">
        <nav className="bg-white/95 backdrop-blur-md shadow-lg rounded-2xl flex items-center justify-between px-6 py-4 md:px-8 lg:px-16" aria-label="Global">
          <div className="flex lg:flex-1">
            <Link href="/" className="-m-1.5 p-1.5">
              <span className="sr-only">Tappel Dakwerken</span>
              <Image
                src="/images/tappel-dakwerken-logo.png"
                alt="Tappel Dakwerken Logo"
                width={80}
                height={80}
                className="w-12 h-12 md:w-20 md:h-20 object-contain"
              />
            </Link>
          </div>
          <div className="flex lg:hidden">
            <button
              type="button"
              className="-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700"
              onClick={() => setMobileMenuOpen(true)}
            >
              <span className="sr-only">Open hoofdmenu</span>
              <Menu className="h-6 w-6" aria-hidden="true" />
            </button>
          </div>
          <div className="hidden lg:flex lg:gap-x-16">
            {navigation.map((item) => (
              <div key={item.name} className="relative">
                {item.dropdown ? (
                  <div
                    className="relative"
                    onMouseEnter={() => setDropdownOpen(item.name)}
                    onMouseLeave={() => setDropdownOpen(null)}
                  >
                    <MagneticNavItem>
                      <button className="flex items-center gap-1 text-sm font-semibold leading-6">
                        {item.name}
                        <ChevronDown className="h-4 w-4" />
                      </button>
                    </MagneticNavItem>

                    {dropdownOpen === item.name && (
                      <div className="absolute top-full left-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                        <div className="p-4">
                          {item.dropdown.map((section) => (
                            <div key={section.name}>
                              <h3 className="text-sm font-semibold text-gray-900 mb-3">{section.name}</h3>
                              <div className="space-y-2">
                                {section.items.map((subItem) => (
                                  <Link
                                    key={subItem.name}
                                    href={subItem.href}
                                    className="block p-2 rounded-md hover:bg-gray-50 transition-colors"
                                  >
                                    <div className="font-medium text-gray-900 hover:text-tappel-orange">
                                      {subItem.name}
                                    </div>
                                    <div className="text-sm text-gray-600">
                                      {subItem.description}
                                    </div>
                                  </Link>
                                ))}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <Link href={item.href}>
                    <MagneticNavItem>
                      <span className="text-sm font-semibold leading-6">
                        {item.name}
                      </span>
                    </MagneticNavItem>
                  </Link>
                )}
              </div>
            ))}
          </div>
          <div className="hidden lg:flex lg:flex-1 lg:justify-end">
            <Link href="/contact/">
              <MagneticButton size="lg" variant="primary">
                CONTACT
              </MagneticButton>
            </Link>
          </div>
        </nav>
      </div>

      {/* Mobile menu */}
      <div className={`lg:hidden ${mobileMenuOpen ? "fixed inset-0 z-50" : "hidden"}`}>
        <div className="fixed inset-0 bg-black/20" onClick={() => setMobileMenuOpen(false)} />
        <div className="fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10">
          <div className="flex items-center justify-between">
            <div className="text-lg font-semibold">Menu</div>
            <button
              type="button"
              className="-m-2.5 rounded-md p-2.5 text-gray-700"
              onClick={() => setMobileMenuOpen(false)}
            >
              <span className="sr-only">Sluit menu</span>
              <X className="h-6 w-6" aria-hidden="true" />
            </button>
          </div>
          <div className="mt-6 flow-root">
            <div className="-my-6 divide-y divide-gray-500/10">
              <div className="space-y-2 py-6">
                {navigation.map((item) => (
                  <div key={item.name}>
                    <Link
                      href={item.href}
                      className="-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      {item.name}
                    </Link>
                    {item.dropdown && (
                      <div className="ml-6 mt-2 space-y-1">
                        {item.dropdown.map((section) => (
                          <div key={section.name}>
                            <div className="text-sm font-medium text-gray-700 px-3 py-1">
                              {section.name}
                            </div>
                            {section.items.map((subItem) => (
                              <Link
                                key={subItem.name}
                                href={subItem.href}
                                className="block px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-md"
                                onClick={() => setMobileMenuOpen(false)}
                              >
                                <div className="font-medium">{subItem.name}</div>
                                <div className="text-xs text-gray-500">{subItem.description}</div>
                              </Link>
                            ))}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
              <div className="py-6">
                <Button className="w-full bg-tappel-orange hover:bg-tappel-orange/90">
                  <Link href="/contact/">CONTACT</Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}

