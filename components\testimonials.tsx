"use client"

import { motion, AnimatePresence } from "framer-motion"
import { useState, useEffect } from "react"
import { Star, Quote, ChevronLeft, ChevronRight } from "lucide-react"
import { AnimatedText } from "@/components/motion/animated-text"
import { AnimatedButton } from "@/components/motion/animated-button"


interface Testimonial {
  id: string
  name: string
  location: string
  rating: number
  review: string
  service: string
  date: string
  avatar?: string
}

const testimonials: Testimonial[] = [
  {
    id: "1",
    name: "<PERSON>",
    location: "Amsterdam",
    rating: 5,
    review: "De inspectie gaf precies inzicht in de problemen en de renovatie is strak uitgevoerd. Sindsdien geen lekkage meer. Het team was professioneel en heeft alles netjes achtergelaten.",
    service: "Dakrenovatie",
    date: "2024"
  },
  {
    id: "2", 
    name: "<PERSON>",
    location: "Utrecht",
    rating: 5,
    review: "Heldere communicatie en snel gepland. Het platte dak is netjes overlaagd en alles is waterdicht opgeleverd. <PERSON><PERSON> tevreden met het resultaat en de service.",
    service: "Overlaging",
    date: "2024"
  },
  {
    id: "3",
    name: "<PERSON>", 
    location: "Rotterdam",
    rating: 5,
    review: "Professioneel, eerlijk en vakkundig. Ook de zonnepanelen zijn zorgvuldig teruggeplaatst. Goede prijs-kwaliteitverhouding en uitstekende nazorg.",
    service: "Zonnepanelen",
    date: "2024"
  },
  {
    id: "4",
    name: "Lisa van der Berg",
    location: "Den Haag", 
    rating: 5,
    review: "Uitstekende service van begin tot eind. Het team was punctueel, netjes en heeft alles perfect achtergelaten. Zou ze zeker aanbevelen aan anderen.",
    service: "Service",
    date: "2024"
  },
  {
    id: "5",
    name: "Tom Hendriks",
    location: "Haarlem",
    rating: 5,
    review: "Snelle reactie op onze lekkage en vakkundige reparatie. Transparante communicatie over kosten en planning. Top service!",
    service: "Lekkage herstel",
    date: "2024"
  }
]

export default function Testimonials() {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)

  // Auto-advance testimonials
  useEffect(() => {
    if (!isAutoPlaying) return
    
    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % testimonials.length)
    }, 5000)
    
    return () => clearInterval(interval)
  }, [isAutoPlaying])

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % testimonials.length)
    setIsAutoPlaying(false)
  }

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length)
    setIsAutoPlaying(false)
  }

  const goToTestimonial = (index: number) => {
    setCurrentIndex(index)
    setIsAutoPlaying(false)
  }

  const currentTestimonial = testimonials[currentIndex]

  return (
    <section className="py-16 md:py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Column - Text Content */}
            <div>
              <AnimatedText variant="fade-up">
                <h2 className="text-3xl md:text-4xl font-bold mb-6">Wat Klanten Zeggen</h2>
              </AnimatedText>
              <AnimatedText variant="fade-up" delay={0.1}>
                <p className="text-lg text-gray-700 mb-8">
                  Lees wat onze klanten over ons zeggen en waarom zij kiezen voor Tappel Dakwerken.
                  Echte reviews van tevreden klanten.
                </p>
              </AnimatedText>
              <AnimatedText variant="fade-up" delay={0.15}>
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                    <span className="text-gray-700">5-sterren reviews</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-gray-700">Tevreden klanten</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <span className="text-gray-700">Professionele service</span>
                  </div>
                </div>
              </AnimatedText>
            </div>

            {/* Right Column - Testimonial Display */}
            <div className="relative">
              <div className="bg-gray-50 rounded-2xl p-8 relative overflow-hidden">
                {/* Quote Icon */}
                <Quote className="absolute top-4 right-4 h-8 w-8 text-tappel-orange/20" />
                
                <AnimatePresence mode="wait">
                  <motion.div
                    key={currentTestimonial.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3 }}
                  >
                    {/* Stars */}
                    <div className="flex gap-1 mb-4">
                      {[...Array(currentTestimonial.rating)].map((_, i) => (
                        <Star key={i} className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                      ))}
                    </div>

                    {/* Review Text */}
                    <blockquote className="text-lg text-gray-800 mb-6 leading-relaxed">
                      "{currentTestimonial.review}"
                    </blockquote>

                    {/* Customer Info */}
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-bold text-gray-900">{currentTestimonial.name}</div>
                        <div className="text-sm text-gray-600">📍 {currentTestimonial.location}</div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium text-tappel-orange">{currentTestimonial.service}</div>
                        <div className="text-sm text-gray-500">{currentTestimonial.date}</div>
                      </div>
                    </div>
                  </motion.div>
                </AnimatePresence>

                {/* Navigation Buttons */}
                <div className="flex justify-between items-center mt-8">
                  <button
                    onClick={prevTestimonial}
                    className="p-2 rounded-full bg-white shadow-md hover:shadow-lg transition-shadow"
                    aria-label="Vorige review"
                  >
                    <ChevronLeft className="h-5 w-5 text-gray-600" />
                  </button>

                  {/* Dots Indicator */}
                  <div className="flex gap-2">
                    {testimonials.map((_, index) => (
                      <button
                        key={index}
                        onClick={() => goToTestimonial(index)}
                        className={`w-2 h-2 rounded-full transition-colors ${
                          index === currentIndex ? 'bg-tappel-orange' : 'bg-gray-300'
                        }`}
                        aria-label={`Ga naar review ${index + 1}`}
                      />
                    ))}
                  </div>

                  <button
                    onClick={nextTestimonial}
                    className="p-2 rounded-full bg-white shadow-md hover:shadow-lg transition-shadow"
                    aria-label="Volgende review"
                  >
                    <ChevronRight className="h-5 w-5 text-gray-600" />
                  </button>
                </div>
              </div>

              {/* Auto-play indicator */}
              {isAutoPlaying && (
                <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2">
                  <div className="flex items-center gap-2 text-xs text-gray-500">
                    <div className="w-1 h-1 bg-green-500 rounded-full animate-pulse"></div>
                    <span>Auto-wisseling actief</span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
