"use client"

import { motion, useMotionValue, useSpring, useTransform } from "framer-motion"
import { useRef, useState, useEffect } from "react"
import { cn } from "@/lib/utils"

interface GlowEffectProps {
  children: React.ReactNode
  className?: string
  mode?: "hover" | "pulse" | "always" | "focus"
  color?: string
  intensity?: "subtle" | "medium" | "strong"
  size?: number
  blur?: number
}

export function GlowEffect({
  children,
  className,
  mode = "hover",
  color = "#FF6B35",
  intensity = "medium",
  size = 20,
  blur = 20
}: GlowEffectProps) {
  const [isActive, setIsActive] = useState(mode === "always")

  const intensityMap = {
    subtle: 0.3,
    medium: 0.6,
    strong: 1.0
  }

  const glowOpacity = intensityMap[intensity]

  // Optimized: Use CSS for pulse animation instead of JS
  const shouldUseCSSPulse = mode === "pulse"

  return (
    <div
      className={cn("relative", className)}
      onMouseEnter={() => mode === "hover" && setIsActive(true)}
      onMouseLeave={() => mode === "hover" && setIsActive(false)}
      onFocus={() => mode === "focus" && setIsActive(true)}
      onBlur={() => mode === "focus" && setIsActive(false)}
    >
      {children}

      {/* Optimized Glow Effect - Single layer, reduced blur */}
      <motion.div
        className={cn(
          "absolute inset-0 rounded-lg pointer-events-none",
          shouldUseCSSPulse && "animate-pulse"
        )}
        style={{
          background: `radial-gradient(circle, ${color}30 0%, transparent 60%)`,
          filter: `blur(${Math.min(blur, 10)}px)`, // Reduced max blur
          zIndex: -1,
          willChange: shouldUseCSSPulse ? 'auto' : 'opacity, transform' // Optimize for GPU
        }}
        initial={{ opacity: 0, scale: 0.9 }}
        animate={
          shouldUseCSSPulse
            ? { opacity: glowOpacity, scale: 1 } // Let CSS handle pulse
            : {
                opacity: isActive ? glowOpacity : 0,
                scale: isActive ? 1.05 : 0.9 // Reduced scale change
              }
        }
        transition={{ duration: 0.2, ease: "easeOut" }} // Faster transition
      />
    </div>
  )
}

interface SpotlightProps {
  children: React.ReactNode
  className?: string
  size?: number
  color?: string
  intensity?: number
}

export function Spotlight({
  children,
  className,
  size = 300,
  color = "#FF6B35",
  intensity = 0.3
}: SpotlightProps) {
  const ref = useRef<HTMLDivElement>(null)
  const mouseX = useMotionValue(0)
  const mouseY = useMotionValue(0)
  const [isHovered, setIsHovered] = useState(false)

  // Throttle mouse move events for better performance
  const handleMouseMove = useRef<(e: React.MouseEvent) => void>()

  if (!handleMouseMove.current) {
    let rafId: number
    handleMouseMove.current = (e: React.MouseEvent) => {
      if (rafId) cancelAnimationFrame(rafId)
      rafId = requestAnimationFrame(() => {
        if (!ref.current) return
        const rect = ref.current.getBoundingClientRect()
        mouseX.set(e.clientX - rect.left)
        mouseY.set(e.clientY - rect.top)
      })
    }
  }

  return (
    <div
      ref={ref}
      className={cn("relative overflow-hidden", className)}
      onMouseMove={handleMouseMove.current}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {children}

      {/* Optimized Spotlight effect - Only render when hovered */}
      {isHovered && (
        <motion.div
          className="absolute pointer-events-none"
          style={{
            width: size,
            height: size,
            background: `radial-gradient(circle, ${color}${Math.round(intensity * 255).toString(16).padStart(2, '0')} 0%, transparent 70%)`,
            x: useTransform(mouseX, (x) => x - size / 2),
            y: useTransform(mouseY, (y) => y - size / 2),
            zIndex: 1,
            willChange: 'transform'
          }}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.15 }}
        />
      )}
    </div>
  )
}

interface BorderTrailProps {
  children?: React.ReactNode
  className?: string
  size?: number
  duration?: number
  color?: string
  delay?: number
}

export function BorderTrail({
  children,
  className,
  size = 100,
  duration = 2,
  color = "#FF6B35",
  delay = 0
}: BorderTrailProps) {
  return (
    <div className={cn("relative overflow-hidden rounded-lg", className)}>
      {children}
      
      {/* Animated border trail */}
      <motion.div
        className="absolute inset-0 rounded-lg"
        style={{
          background: `conic-gradient(from 0deg, transparent, ${color}, transparent)`,
          padding: "2px"
        }}
        animate={{ rotate: 360 }}
        transition={{
          duration,
          repeat: Infinity,
          ease: "linear",
          delay
        }}
      >
        <div className="w-full h-full bg-white rounded-lg" />
      </motion.div>
    </div>
  )
}

interface ShimmerProps {
  children: React.ReactNode
  className?: string
  color?: string
  duration?: number
  width?: string
}

export function Shimmer({
  children,
  className,
  color = "#FF6B35",
  duration = 2,
  width = "100px"
}: ShimmerProps) {
  return (
    <div className={cn("relative overflow-hidden", className)}>
      {children}
      
      {/* Shimmer effect */}
      <motion.div
        className="absolute inset-0 -skew-x-12"
        style={{
          background: `linear-gradient(90deg, transparent, ${color}40, transparent)`,
          width: width
        }}
        initial={{ x: "-100%" }}
        animate={{ x: "calc(100% + 100px)" }}
        transition={{
          duration,
          repeat: Infinity,
          ease: "easeInOut",
          repeatDelay: 1
        }}
      />
    </div>
  )
}

interface PulsingDotProps {
  className?: string
  color?: string
  size?: number
  pulseScale?: number
}

export function PulsingDot({
  className,
  color = "#FF6B35",
  size = 12,
  pulseScale = 2
}: PulsingDotProps) {
  return (
    <div className={cn("relative", className)}>
      {/* Main dot */}
      <div
        className="rounded-full"
        style={{
          width: size,
          height: size,
          backgroundColor: color
        }}
      />
      
      {/* Pulsing ring */}
      <motion.div
        className="absolute inset-0 rounded-full border-2"
        style={{
          borderColor: color,
          width: size,
          height: size
        }}
        animate={{
          scale: [1, pulseScale, 1],
          opacity: [1, 0, 1]
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
    </div>
  )
}

interface GradientBorderProps {
  children: React.ReactNode
  className?: string
  colors?: string[]
  width?: number
  animated?: boolean
}

export function GradientBorder({
  children,
  className,
  colors = ["#FF6B35", "#F7931E", "#FF6B35"],
  width = 2,
  animated = true
}: GradientBorderProps) {
  const gradientColors = colors.join(", ")
  
  return (
    <div className={cn("relative", className)}>
      <motion.div
        className="absolute inset-0 rounded-lg"
        style={{
          background: `linear-gradient(45deg, ${gradientColors})`,
          padding: `${width}px`
        }}
        animate={animated ? { rotate: 360 } : {}}
        transition={animated ? {
          duration: 3,
          repeat: Infinity,
          ease: "linear"
        } : {}}
      >
        <div className="w-full h-full bg-white rounded-lg">
          {children}
        </div>
      </motion.div>
    </div>
  )
}

interface NeonTextProps {
  children: React.ReactNode
  className?: string
  color?: string
  intensity?: "subtle" | "medium" | "strong"
}

export function NeonText({
  children,
  className,
  color = "#FF6B35",
  intensity = "medium"
}: NeonTextProps) {
  const intensityMap = {
    subtle: "0 0 5px",
    medium: "0 0 10px",
    strong: "0 0 20px"
  }
  
  const shadowIntensity = intensityMap[intensity]
  
  return (
    <motion.div
      className={cn("relative", className)}
      style={{
        color: color,
        textShadow: `${shadowIntensity} ${color}, ${shadowIntensity} ${color}, ${shadowIntensity} ${color}`
      }}
      whileHover={{
        textShadow: `0 0 20px ${color}, 0 0 30px ${color}, 0 0 40px ${color}`
      }}
      transition={{ duration: 0.3 }}
    >
      {children}
    </motion.div>
  )
}
