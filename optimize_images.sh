#!/bin/bash

# Script om alle grote afbeeldingen te optimaliseren voor web gebruik
# Doel: afbeeldingen verkleinen tot max 500KB voor betere laadtijden

echo "Starting image optimization..."

# Functie om een afbeelding te optimaliseren
optimize_image() {
    local input_file="$1"
    local output_file="$2"
    
    echo "Optimizing: $input_file"
    
    # Controleer bestandsgrootte (in bytes)
    local file_size=$(stat -c%s "$input_file" 2>/dev/null || stat -f%z "$input_file" 2>/dev/null)
    local file_size_kb=$((file_size / 1024))
    
    echo "  Original size: ${file_size_kb}KB"
    
    # Als bestand groter is dan 500KB, optimaliseer het
    if [ $file_size_kb -gt 500 ]; then
        # Maak backup van origineel
        cp "$input_file" "${input_file}.backup"
        
        # Optimaliseer afbeelding: resize naar max 1920px breed, kwaliteit 85%, strip metadata
        magick "$input_file" \
            -resize "1920x1920>" \
            -quality 85 \
            -strip \
            -interlace Plane \
            "$output_file"
        
        # Controleer nieuwe grootte
        local new_size=$(stat -c%s "$output_file" 2>/dev/null || stat -f%z "$output_file" 2>/dev/null)
        local new_size_kb=$((new_size / 1024))
        
        echo "  New size: ${new_size_kb}KB"
        echo "  Saved: $((file_size_kb - new_size_kb))KB"
        
        # Vervang origineel met geoptimaliseerde versie
        mv "$output_file" "$input_file"
    else
        echo "  File already optimized (${file_size_kb}KB)"
    fi
}

# Vind alle JPG en PNG bestanden en optimaliseer ze
find public/images -type f \( -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" \) | while read -r file; do
    optimize_image "$file" "${file}.tmp"
done

echo "Image optimization completed!"
echo ""
echo "Summary of optimized files:"
find public/images -name "*.backup" | wc -l | xargs echo "Backup files created:"
