"use client";

import { useEffect, useRef } from 'react';

declare global {
  interface Window {
    grecaptcha: any;
    onRecaptchaLoad: () => void;
    onRecaptchaSuccess: (token: string) => void;
    onRecaptchaExpired: () => void;
    onRecaptchaError: () => void;
  }
}

export default function ReCaptcha() {
  useEffect(() => {
    // Check if script is already loaded
    if (document.querySelector('script[src*="recaptcha"]')) {
      return;
    }

    // Load reCAPTCHA script for invisible reCAPTCHA
    const script = document.createElement('script');
    script.src = 'https://www.google.com/recaptcha/api.js?onload=onRecaptchaLoad&render=explicit';
    script.async = true;
    script.defer = true;
    document.head.appendChild(script);

    return () => {
      // Cleanup
      const existingScript = document.querySelector('script[src*="recaptcha"]');
      if (existingScript) {
        document.head.removeChild(existingScript);
      }
    };
  }, []);

  return null;
}

// Invisible reCAPTCHA hook for forms
export function useInvisibleRecaptcha() {
  const widgetIdRef = useRef<number | null>(null);
  const callbackRef = useRef<((token: string) => void) | null>(null);

  useEffect(() => {
    // Define the callback function for when reCAPTCHA is loaded
    window.onRecaptchaLoad = () => {
      if (window.grecaptcha && !widgetIdRef.current) {
        try {
          const siteKey = process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY || '6LeRiL0rAAAAAJ5xM7FK0ZG5HJ_aNI393vEhTJrD';

          // Create invisible reCAPTCHA widget
          widgetIdRef.current = window.grecaptcha.render('recaptcha-container', {
            sitekey: siteKey,
            size: 'invisible',
            callback: (token: string) => {
              console.log('reCAPTCHA success:', token);
              if (callbackRef.current) {
                callbackRef.current(token);
              }
            },
            'expired-callback': () => {
              console.log('reCAPTCHA expired');
              window.onRecaptchaExpired();
            },
            'error-callback': () => {
              console.log('reCAPTCHA error');
              window.onRecaptchaError();
            }
          });
        } catch (error) {
          console.error('reCAPTCHA render error:', error);
        }
      }
    };

    // If grecaptcha is already loaded, render immediately
    if (window.grecaptcha && window.grecaptcha.render && !widgetIdRef.current) {
      window.onRecaptchaLoad();
    }

    return () => {
      // Cleanup widget when component unmounts
      if (widgetIdRef.current !== null && window.grecaptcha && window.grecaptcha.reset) {
        try {
          window.grecaptcha.reset(widgetIdRef.current);
        } catch (error) {
          console.error('reCAPTCHA cleanup error:', error);
        }
      }
    };
  }, []);

  const executeRecaptcha = (callback: (token: string) => void) => {
    callbackRef.current = callback;
    if (window.grecaptcha && widgetIdRef.current !== null) {
      try {
        window.grecaptcha.execute(widgetIdRef.current);
      } catch (error) {
        console.error('reCAPTCHA execute error:', error);
        callback(''); // Call with empty token on error
      }
    } else {
      console.error('reCAPTCHA not ready');
      callback(''); // Call with empty token if not ready
    }
  };

  const resetRecaptcha = () => {
    if (window.grecaptcha && widgetIdRef.current !== null) {
      try {
        window.grecaptcha.reset(widgetIdRef.current);
      } catch (error) {
        console.error('reCAPTCHA reset error:', error);
      }
    }
  };

  return { executeRecaptcha, resetRecaptcha };
}

// Invisible reCAPTCHA container component
export function InvisibleReCaptchaContainer() {
  return <div id="recaptcha-container" style={{ display: 'none' }} />;
}

// Global reCAPTCHA callback functions
if (typeof window !== 'undefined') {
  window.onRecaptchaLoad = function() {
    console.log('reCAPTCHA loaded');
  };

  window.onRecaptchaSuccess = function(token: string) {
    console.log('reCAPTCHA success:', token);
  };

  window.onRecaptchaExpired = function() {
    console.log('reCAPTCHA expired');
  };

  window.onRecaptchaError = function() {
    console.log('reCAPTCHA error occurred');
  };
}
