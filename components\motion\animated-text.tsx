"use client"

import { motion, type Variants } from "framer-motion"
import { cn } from "@/lib/utils"

interface AnimatedTextProps {
  children: React.ReactNode
  className?: string
  as?: keyof JSX.IntrinsicElements
  variant?: "fade-up" | "fade-in" | "slide-left" | "slide-right" | "scale"
  delay?: number
  duration?: number
  stagger?: number
  once?: boolean
}

const textVariants: Record<string, Variants> = {
  "fade-up": {
    hidden: { 
      opacity: 0, 
      y: 30 
    },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 12
      }
    }
  },
  "fade-in": {
    hidden: { 
      opacity: 0 
    },
    visible: { 
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  },
  "slide-left": {
    hidden: { 
      opacity: 0, 
      x: -50 
    },
    visible: { 
      opacity: 1, 
      x: 0,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 12
      }
    }
  },
  "slide-right": {
    hidden: { 
      opacity: 0, 
      x: 50 
    },
    visible: { 
      opacity: 1, 
      x: 0,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 12
      }
    }
  },
  "scale": {
    hidden: { 
      opacity: 0, 
      scale: 0.8 
    },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 12
      }
    }
  }
}

export function AnimatedText({
  children,
  className,
  as: Component = "div",
  variant = "fade-up",
  delay = 0,
  duration,
  stagger,
  once = true
}: AnimatedTextProps) {
  const variants = textVariants[variant]

  // Create a copy to avoid mutating the original
  const animationVariants = duration ? {
    ...variants,
    visible: {
      ...variants.visible,
      transition: {
        ...variants.visible.transition,
        duration
      }
    }
  } : variants

  return (
    <motion.div
      className={cn(className)}
      variants={animationVariants}
      initial="hidden"
      whileInView="visible"
      viewport={{
        once,
        margin: "-50px", // Reduced margin for earlier trigger
        amount: 0.1 // Only need 10% visible to trigger
      }}
      transition={{ delay }}
    >
      {Component === "div" ? (
        children
      ) : (
        <Component className={className}>
          {children}
        </Component>
      )}
    </motion.div>
  )
}

// Specialized component for staggered text animations
interface AnimatedTextStaggerProps {
  children: React.ReactNode
  className?: string
  variant?: "fade-up" | "fade-in" | "slide-left" | "slide-right" | "scale"
  stagger?: number
  delay?: number
  once?: boolean
}

export function AnimatedTextStagger({
  children,
  className,
  variant = "fade-up",
  stagger = 0.1,
  delay = 0,
  once = true
}: AnimatedTextStaggerProps) {
  const containerVariants: Variants = {
    hidden: {},
    visible: {
      transition: {
        staggerChildren: stagger,
        delayChildren: delay
      }
    }
  }

  const itemVariants = textVariants[variant]

  return (
    <motion.div
      className={cn(className)}
      variants={containerVariants}
      initial="hidden"
      whileInView="visible"
      viewport={{ once, margin: "-100px" }}
    >
      {Array.isArray(children) ? (
        children.map((child, index) => (
          <motion.div key={index} variants={itemVariants}>
            {child}
          </motion.div>
        ))
      ) : (
        <motion.div variants={itemVariants}>
          {children}
        </motion.div>
      )}
    </motion.div>
  )
}
