import { NextRequest, NextResponse } from 'next/server';
import { Resend } from 'resend';

const resend = new Resend(process.env.RESEND_API_KEY);

export async function POST(request: NextRequest) {
  try {
    // Check if API key is configured
    if (!process.env.RESEND_API_KEY) {
      console.error('RESEND_API_KEY is not configured');
      return NextResponse.json(
        { error: 'Email service is niet geconfigureerd. Neem direct contact op via telefoon.' },
        { status: 500 }
      );
    }

    const body = await request.json();
    const { firstName, lastName, email, phone, subject, message, recaptchaToken } = body;

    // reCAPTCHA validatie
    if (!recaptchaToken) {
      return NextResponse.json(
        { error: 'reCAPTCHA verificatie is vereist.' },
        { status: 400 }
      );
    }

    // Verificeer reCAPTCHA token met Google
    const recaptchaSecret = process.env.RECAPTCHA_SECRET_KEY;
    if (recaptchaSecret) {
      try {
        const recaptchaResponse = await fetch('https://www.google.com/recaptcha/api/siteverify', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: `secret=${recaptchaSecret}&response=${recaptchaToken}`,
        });

        const recaptchaData = await recaptchaResponse.json();

        if (!recaptchaData.success) {
          return NextResponse.json(
            { error: 'reCAPTCHA verificatie mislukt. Probeer opnieuw.' },
            { status: 400 }
          );
        }
      } catch (error) {
        console.error('reCAPTCHA verification error:', error);
        return NextResponse.json(
          { error: 'reCAPTCHA verificatie fout. Probeer opnieuw.' },
          { status: 500 }
        );
      }
    }

    // Validatie
    if (!firstName || !lastName || !email || !subject || !message) {
      return NextResponse.json(
        { error: 'Alle verplichte velden moeten worden ingevuld.' },
        { status: 400 }
      );
    }

    // Email validatie
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Voer een geldig e-mailadres in.' },
        { status: 400 }
      );
    }

    // Email versturen naar Tappel Dakwerken
    const { data, error } = await resend.emails.send({
      from: 'Contactformulier <<EMAIL>>',
      to: ['<EMAIL>'],
      subject: `Nieuw contactformulier: ${subject}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #f97316; border-bottom: 2px solid #f97316; padding-bottom: 10px;">
            Nieuw bericht via contactformulier
          </h2>
          
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin-top: 0; color: #333;">Contactgegevens</h3>
            <p><strong>Naam:</strong> ${firstName} ${lastName}</p>
            <p><strong>E-mail:</strong> ${email}</p>
            <p><strong>Telefoon:</strong> ${phone || 'Niet opgegeven'}</p>
            <p><strong>Onderwerp:</strong> ${subject}</p>
          </div>
          
          <div style="background-color: #ffffff; padding: 20px; border: 1px solid #e9ecef; border-radius: 8px;">
            <h3 style="margin-top: 0; color: #333;">Bericht</h3>
            <p style="white-space: pre-wrap; line-height: 1.6;">${message}</p>
          </div>
          
          <div style="margin-top: 20px; padding: 15px; background-color: #e7f3ff; border-radius: 8px; border-left: 4px solid #0066cc;">
            <p style="margin: 0; font-size: 14px; color: #666;">
              Dit bericht is verzonden via het contactformulier op tappeldakwerken.nl op ${new Date().toLocaleString('nl-NL')}.
            </p>
          </div>
        </div>
      `,
    });

    if (error) {
      console.error('Resend error:', error);
      return NextResponse.json(
        { error: 'Er is een fout opgetreden bij het verzenden van uw bericht. Probeer het later opnieuw.' },
        { status: 500 }
      );
    }

    // Bevestigingsmail naar de klant
    await resend.emails.send({
      from: 'Tappel Dakwerken <<EMAIL>>',
      to: [email],
      subject: 'Bevestiging: Uw bericht is ontvangen',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #f97316; border-bottom: 2px solid #f97316; padding-bottom: 10px;">
            Bedankt voor uw bericht!
          </h2>
          
          <p>Beste ${firstName},</p>
          
          <p>Bedankt voor uw bericht via ons contactformulier. We hebben uw aanvraag in goede orde ontvangen en zullen zo spoedig mogelijk contact met u opnemen.</p>
          
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin-top: 0; color: #333;">Uw bericht</h3>
            <p><strong>Onderwerp:</strong> ${subject}</p>
            <p><strong>Bericht:</strong></p>
            <p style="white-space: pre-wrap; line-height: 1.6; background-color: white; padding: 15px; border-radius: 4px;">${message}</p>
          </div>
          
          <div style="background-color: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin-top: 0; color: #333;">Contactgegevens</h3>
            <p><strong>Tappel Dakwerken</strong></p>
            <p>📧 <EMAIL></p>
            <p>📞 06-21452819</p>
            <p>📍 Trondheimpad 13, 3067DM Rotterdam</p>
          </div>
          
          <p>Voor spoedeisende zaken kunt u ons direct bellen op <strong>06-21452819</strong>.</p>
          
          <p>Met vriendelijke groet,<br>
          Het team van Tappel Dakwerken</p>
          
          <div style="margin-top: 30px; padding: 15px; background-color: #f0f0f0; border-radius: 8px; font-size: 12px; color: #666;">
            <p style="margin: 0;">
              Dit is een automatisch gegenereerd bericht. Reageer niet op deze e-mail.
              Voor vragen kunt u contact <NAME_EMAIL> of 06-21452819.
            </p>
          </div>
        </div>
      `,
    });

    return NextResponse.json(
      { message: 'Uw bericht is succesvol verzonden. We nemen zo spoedig mogelijk contact met u op.' },
      { status: 200 }
    );

  } catch (error) {
    console.error('Contact form error:', error);
    return NextResponse.json(
      { error: 'Er is een onverwachte fout opgetreden. Probeer het later opnieuw.' },
      { status: 500 }
    );
  }
}
