const fs = require('fs');
const path = require('path');

// Stad-specifieke diensten
const cityServices = {
  'utrecht': [
    {
      title: 'Binnenstad Renovatie',
      description: 'Dakrenovatie voor historische panden in Utrecht centrum. Van Dom omgeving tot Oudegracht. Respectvol voor monumentale waarde.'
    },
    {
      title: 'Leidsche Rijn Nieuwbouw',
      description: 'EPDM en bitumen renovaties in moderne wijk Leidsche Rijn. Jaren 2000+ constructies met specifieke isolatie-eisen.'
    },
    {
      title: 'Universiteitsgebouwen',
      description: 'Specialistische renovatie van universiteits- en onderzoeksgebouwen. Minimale overlast tijdens collegejaar.'
    }
  ],
  'haarlem': [
    {
      title: 'Bloemenstad Renovatie',
      description: 'Dakrenovatie in het hart van de bloemenstad. Van historisch centrum tot moderne wijken in Schalkwijk.'
    },
    {
      title: '<PERSON>le Gebouwen',
      description: '<PERSON><PERSON><PERSON> van musea, theaters en culturele instellingen. Expertise in monumentale en moderne architectuur.'
    },
    {
      title: 'Haarlem Noord',
      description: 'EPDM renovaties in noordelijke wijken. Moderne woningbouw met specifieke dakconstructies.'
    }
  ],
  'leiden': [
    {
      title: 'Sleutelstad Expertise',
      description: 'Dakrenovatie in de sleutelstad. Van historische binnenstad tot moderne Bio Science Park.'
    },
    {
      title: 'Universiteit Leiden',
      description: 'Specialistische renovatie van universiteitsgebouwen en studentenhuisvesting. 400+ jaar ervaring met onderwijsgebouwen.'
    },
    {
      title: 'Bio Science Park',
      description: 'High-tech dakrenovaties voor laboratoria en onderzoeksfaciliteiten. Strikte eisen voor cleanroom omgevingen.'
    }
  ],
  'delft': [
    {
      title: 'Studentenstad Renovatie',
      description: 'Dakrenovatie voor studentenhuisvesting en onderwijsgebouwen. Van TU campus tot historisch centrum.'
    },
    {
      title: 'TU Delft Campus',
      description: 'Technische dakrenovaties voor universiteitsgebouwen. Expertise in innovatieve dakconstructies en materialen.'
    },
    {
      title: 'Tech Bedrijven',
      description: 'EPDM renovaties voor tech-bedrijven en start-ups. Moderne kantoorpanden met specifieke eisen.'
    }
  ],
  'alkmaar': [
    {
      title: 'Kaasstad Renovatie',
      description: 'Dakrenovatie in de kaasstad. Van historische panden rond de Kaasmarkt tot moderne wijken in Daalmeer.'
    },
    {
      title: 'Historisch Centrum',
      description: 'Monumentale dakrenovaties in Alkmaar centrum. Respectvol voor de rijke geschiedenis van de kaasstad.'
    },
    {
      title: 'Moderne Wijken',
      description: 'EPDM en bitumen renovaties in Overdie, Daalmeer en Bergerhout. Jaren 80-2000 constructies.'
    }
  ],
  'gouda': [
    {
      title: 'Kaas & Kaarsen Stad',
      description: 'Dakrenovatie in Gouda. Van historische panden tot moderne wijken. Expertise in diverse bouwperiodes.'
    },
    {
      title: 'Monumentaal Gouda',
      description: 'Renovatie van monumentale panden in het centrum. Van middeleeuws tot 19e eeuw architectuur.'
    },
    {
      title: 'Moderne Uitbreidingen',
      description: 'EPDM renovaties in Korte Akkeren en Goverwelle. Nieuwbouw uit verschillende decennia.'
    }
  ]
};

// Functie om diensten-blokken te lokaliseren
function localizeServices(cityName) {
  const filePath = path.join(__dirname, '..', 'app', `dakrenovatie-${cityName}`, 'page.tsx');
  
  if (!fs.existsSync(filePath)) {
    console.log(`Bestand niet gevonden: ${filePath}`);
    return;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  const services = cityServices[cityName];
  
  if (!services) {
    console.log(`Geen services gedefinieerd voor: ${cityName}`);
    return;
  }

  // Vervang eerste generieke dienst-blok
  content = content.replace(
    /<h3 className="text-xl font-bold mb-3">Complete Dakrenovatie<\/h3>\s*<p className="text-gray-600 mb-4">\s*[^<]*<\/p>/,
    `<h3 className="text-xl font-bold mb-3">${services[0].title}</h3>
                <p className="text-gray-600 mb-4">
                  ${services[0].description}
                </p>`
  );

  // Vervang tweede generieke dienst-blok
  content = content.replace(
    /<h3 className="text-xl font-bold mb-3">EPDM Dakrenovatie<\/h3>\s*<p className="text-gray-600 mb-4">\s*[^<]*<\/p>/,
    `<h3 className="text-xl font-bold mb-3">${services[1].title}</h3>
                <p className="text-gray-600 mb-4">
                  ${services[1].description}
                </p>`
  );

  // Vervang derde generieke dienst-blok
  content = content.replace(
    /<h3 className="text-xl font-bold mb-3">Bitumen Dakrenovatie<\/h3>\s*<p className="text-gray-600 mb-4">\s*[^<]*<\/p>/,
    `<h3 className="text-xl font-bold mb-3">${services[2].title}</h3>
                <p className="text-gray-600 mb-4">
                  ${services[2].description}
                </p>`
  );

  fs.writeFileSync(filePath, content);
  console.log(`Diensten gelokaliseerd voor: ${cityName}`);
}

// Lokaliseer alle steden
Object.keys(cityServices).forEach(cityName => {
  localizeServices(cityName);
});

console.log('Alle diensten-blokken zijn gelokaliseerd!');
