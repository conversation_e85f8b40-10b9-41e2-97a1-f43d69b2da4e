import Image from "next/image"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowRight, Calendar, User, Tag } from "lucide-react"
import type { Metadata } from "next"
import { blogPosts } from "@/lib/blog-data"

export const metadata: Metadata = {
  title: "Blog | Dakrenovatie Tips & Dakdoorvoer Advies | Tappel Dakwerken",
  description: "Tappel Dakwerken blog: dakrenovatie tips, dakdoorvoer advies, EPDM en bitumen kennis, lekkage voorkomen en montageanker informatie. Professionele daktips van experts.",
  keywords: "dakrenovatie tips, dakdoorvoer advies, EPDM dak onderhoud, bitumen dak tips, lekkage voorkomen, montageanker informatie, plat dak onderhoud, groendak tips",
  authors: [{ name: "Tappel Dakwerken" }],
  creator: "Tappel Dakwerken",
  publisher: "Tappel Dakwerken",
  alternates: {
    canonical: "/blog",
  },
  openGraph: {
    title: "Blog | Tappel Dakwerken | Dakrenovatie Tips & Dakdoorvoer Advies",
    description: "Tappel Dakwerken blog: dakrenovatie tips, dakdoorvoer advies, EPDM en bitumen kennis, lekkage voorkomen en montageanker informatie. Professionele daktips van experts.",
    url: "https://tappeldakwerken.nl/blog",
    siteName: "Tappel Dakwerken",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Tappel Dakwerken Blog - Dakrenovatie Tips",
      },
    ],
    locale: "nl_NL",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Blog | Tappel Dakwerken",
    description: "Tappel Dakwerken blog: dakrenovatie tips, dakdoorvoer advies, EPDM en bitumen kennis. Professionele daktips van experts.",
    images: ["/og-image.jpg"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  category: "Dakwerken Blog",
}

export default function BlogPage() {
  return (
    <>
      {/* Hero Section */}
      <section className="bg-gray-100 py-12 md:py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Onze Blog</h1>
            <p className="text-xl text-gray-700">
              Professionele tips, advies en informatie over dakdoorvoeren, zonnepanelen, dakrenovaties en het
              onderhouden van uw dak.
            </p>
          </div>
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section className="py-16 md:py-24">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {blogPosts.map((post) => (
              <div
                key={post.slug}
                className="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow"
              >
                <Link href={`/blog/${post.slug}`} className="block relative h-48 overflow-hidden">
                  <Image
                    src={post.coverImage || "/placeholder.svg"}
                    alt={post.title}
                    fill
                    className="object-cover transition-transform hover:scale-105 duration-300"
                  />
                </Link>
                <div className="p-6">
                  <div className="flex flex-wrap gap-2 mb-3">
                    {post.categories.map((category) => (
                      <span key={category} className="inline-flex items-center text-xs font-medium text-tappel-orange">
                        <Tag className="mr-1 h-3 w-3" />
                        {category}
                      </span>
                    ))}
                  </div>
                  <Link href={`/blog/${post.slug}`}>
                    <h2 className="text-xl font-bold mb-3 hover:text-tappel-orange transition-colors">{post.title}</h2>
                  </Link>
                  <p className="text-gray-600 mb-4">{post.excerpt}</p>
                  <div className="flex justify-between items-center">
                    <div className="flex items-center text-sm text-gray-500">
                      <Calendar className="mr-1 h-4 w-4" />
                      <span>{post.date}</span>
                    </div>
                    <div className="flex items-center text-sm text-gray-500">
                      <User className="mr-1 h-4 w-4" />
                      <span>{post.author}</span>
                    </div>
                  </div>
                  <Button asChild variant="outline" className="group w-full mt-4">
                    <Link href={`/blog/${post.slug}`}>
                      Lees meer <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                    </Link>
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="py-16 md:py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-4">Blijf op de hoogte</h2>
            <p className="text-lg text-gray-700 mb-8">
              Schrijf u in voor onze nieuwsbrief en ontvang regelmatig tips, advies en nieuws over dakonderhoud en
              duurzame dakoplossingen.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-lg mx-auto">
              <input
                type="email"
                placeholder="Uw e-mailadres"
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              />
              <Button className="whitespace-nowrap">Inschrijven</Button>
            </div>
            <p className="text-xs text-gray-500 mt-4">
              Door u in te schrijven gaat u akkoord met onze privacybeleid. U kunt zich op elk moment uitschrijven.
            </p>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 md:py-24 bg-tappel-orange text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">Heeft u een vraag over uw dak?</h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            Neem contact met ons op voor persoonlijk advies of een vrijblijvende offerte. Onze dakspecialisten staan
            voor u klaar!
          </p>
          <Button
            asChild
            size="xl"
            variant="outline"
            className="bg-white text-tappel-orange hover:bg-gray-100 border-white"
          >
            <Link href="/contact">CONTACT OPNEMEN</Link>
          </Button>
        </div>
      </section>
    </>
  )
}

