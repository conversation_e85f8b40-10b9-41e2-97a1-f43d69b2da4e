"use client"

import { motion, type Variants } from "framer-motion"
import { forwardRef } from "react"
import { Slot } from "@radix-ui/react-slot"
import { cn } from "@/lib/utils"

interface AnimatedButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "primary" | "secondary" | "outline" | "ghost"
  size?: "sm" | "md" | "lg" | "xl"
  children: React.ReactNode
  asChild?: boolean
  href?: string
  glow?: boolean
  magnetic?: boolean
}

const buttonVariants: Variants = {
  initial: { 
    scale: 1,
    boxShadow: "0 0 0 rgba(255, 107, 53, 0)"
  },
  hover: { 
    scale: 1.02,
    boxShadow: "0 8px 25px rgba(255, 107, 53, 0.15)",
    transition: {
      type: "spring",
      stiffness: 400,
      damping: 25
    }
  },
  tap: { 
    scale: 0.98,
    transition: {
      type: "spring",
      stiffness: 600,
      damping: 30
    }
  }
}

const glowVariants: Variants = {
  initial: { 
    opacity: 0,
    scale: 0.8
  },
  hover: { 
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.3,
      ease: "easeOut"
    }
  }
}

const AnimatedButton = forwardRef<HTMLButtonElement, AnimatedButtonProps>(
  ({
    className,
    variant = "primary",
    size = "md",
    children,
    glow = false,
    magnetic = false,
    disabled,
    asChild = false,
    ...props
  }, ref) => {

    const baseClasses = "relative inline-flex items-center justify-center rounded-lg font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-tappel-orange focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"

    const variantClasses = {
      primary: "bg-tappel-orange text-white hover:bg-tappel-orange/90",
      secondary: "bg-tappel-blue text-white hover:bg-tappel-blue/90",
      outline: "border-2 border-tappel-orange text-tappel-orange hover:bg-tappel-orange hover:text-white",
      ghost: "text-tappel-orange hover:bg-tappel-orange/10"
    }

    const sizeClasses = {
      sm: "h-9 px-3 text-sm",
      md: "h-10 px-4 py-2",
      lg: "h-11 px-6 text-lg",
      xl: "h-14 px-8 text-xl"
    }

    if (asChild) {
      // When using asChild, we need to pass the className and other props to the child
      // The Slot component expects exactly one child element
      return (
        <Slot
          ref={ref}
          className={cn(
            baseClasses,
            variantClasses[variant],
            sizeClasses[size],
            className
          )}
          {...props}
        >
          {children}
        </Slot>
      )
    }

    const ButtonComponent = (
      <motion.button
        ref={ref}
        className={cn(
          baseClasses,
          variantClasses[variant],
          sizeClasses[size],
          className
        )}
        variants={buttonVariants}
        initial="initial"
        whileHover={disabled ? undefined : "hover"}
        whileTap={disabled ? undefined : "tap"}
        disabled={disabled}
        {...props}
      >
        {/* Glow Effect Background */}
        {glow && (
          <motion.div
            className="absolute inset-0 rounded-lg bg-gradient-to-r from-tappel-orange to-orange-400 blur-lg"
            variants={glowVariants}
            initial="initial"
            whileHover="hover"
            style={{ zIndex: -1 }}
          />
        )}

        {/* Button Content */}
        <span className="relative z-10">
          {children}
        </span>
      </motion.button>
    )

    // Wrap with magnetic effect if enabled
    if (magnetic && !disabled) {
      return (
        <motion.div
          whileHover={{ scale: 1.05 }}
          transition={{ type: "spring", stiffness: 300 }}
        >
          {ButtonComponent}
        </motion.div>
      )
    }

    return ButtonComponent
  }
)

AnimatedButton.displayName = "AnimatedButton"

export { AnimatedButton }
