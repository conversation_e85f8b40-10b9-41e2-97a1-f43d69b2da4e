"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Phone, Mail, MapPin, Clock, Send, CheckCircle, AlertCircle } from "lucide-react"
import { AnimatedText } from "@/components/motion/animated-text"
import { AnimatedButton } from "@/components/motion/animated-button"
import { AnimatedCard } from "@/components/motion/animated-card"


interface ContactFormData {
  firstName: string
  lastName: string
  email: string
  phone: string
  subject: string
  message: string
}

export default function ModernContact() {
  const [formData, setFormData] = useState<ContactFormData>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle')

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    // Simulate API call
    setTimeout(() => {
      setSubmitStatus('success')
      setIsSubmitting(false)
      // Reset form after success
      setTimeout(() => {
        setFormData({
          firstName: '',
          lastName: '',
          email: '',
          phone: '',
          subject: '',
          message: ''
        })
        setSubmitStatus('idle')
      }, 2000)
    }, 800)
  }

  const contactInfo = [
    {
      icon: <Phone className="h-6 w-6" />,
      title: "Telefoon",
      value: "06-21452819",
      description: "Ma-Vr 8:00-18:00",
      href: "tel:0621452819"
    },
    {
      icon: <Mail className="h-6 w-6" />,
      title: "Email",
      value: "<EMAIL>",
      description: "Binnen 24u reactie",
      href: "mailto:<EMAIL>"
    },
    {
      icon: <MapPin className="h-6 w-6" />,
      title: "Werkgebied",
      value: "Noord & Zuid-Holland",
      description: "Utrecht provincie",
      href: null
    },
    {
      icon: <Clock className="h-6 w-6" />,
      title: "Openingstijden",
      value: "Ma-Vr 8:00-18:00",
      description: "Za op afspraak",
      href: null
    }
  ]

  return (
    <section className="py-16 md:py-24 bg-gradient-to-br from-gray-50 to-white">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="text-center mb-16">
            <AnimatedText variant="fade-up">
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                Contact <span className="text-tappel-orange">Opnemen</span>
              </h1>
            </AnimatedText>
            <AnimatedText variant="fade-up" delay={0.05}>
              <p className="text-xl text-gray-700 max-w-3xl mx-auto">
                Klaar voor een gratis offerte of advies? Neem contact op voor dakrenovatie,
                dakdoorvoeren, montageankers of lekkage herstel. We reageren binnen 24 uur.
              </p>
            </AnimatedText>
          </div>

          <div className="grid lg:grid-cols-2 gap-16 items-start">
            {/* Contact Form */}
            <div>
              <AnimatedText variant="fade-up" delay={0.1}>
                <h2 className="text-2xl font-bold mb-8">Vraag Gratis Offerte Aan</h2>
              </AnimatedText>

                <AnimatedCard variant="hover-lift" className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300">
                  <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Name Fields */}
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-2">
                          Voornaam *
                        </label>
                        <input
                          type="text"
                          id="firstName"
                          name="firstName"
                          value={formData.firstName}
                          onChange={handleInputChange}
                          required
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-tappel-orange focus:border-transparent transition-colors"
                          placeholder="Uw voornaam"
                        />
                      </div>
                      <div>
                        <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-2">
                          Achternaam *
                        </label>
                        <input
                          type="text"
                          id="lastName"
                          name="lastName"
                          value={formData.lastName}
                          onChange={handleInputChange}
                          required
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-tappel-orange focus:border-transparent transition-colors"
                          placeholder="Uw achternaam"
                        />
                      </div>
                    </div>

                    {/* Contact Fields */}
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                          Email *
                        </label>
                        <input
                          type="email"
                          id="email"
                          name="email"
                          value={formData.email}
                          onChange={handleInputChange}
                          required
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-tappel-orange focus:border-transparent transition-colors"
                          placeholder="<EMAIL>"
                        />
                      </div>
                      <div>
                        <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                          Telefoon
                        </label>
                        <input
                          type="tel"
                          id="phone"
                          name="phone"
                          value={formData.phone}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-tappel-orange focus:border-transparent transition-colors"
                          placeholder="06-12345678"
                        />
                      </div>
                    </div>

                    {/* Subject */}
                    <div>
                      <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">
                        Onderwerp *
                      </label>
                      <select
                        id="subject"
                        name="subject"
                        value={formData.subject}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-tappel-orange focus:border-transparent transition-colors"
                      >
                        <option value="">Selecteer onderwerp</option>
                        <option value="dakrenovatie">Dakrenovatie</option>
                        <option value="dakdoorvoer">Dakdoorvoer</option>
                        <option value="montageanker">Montageanker</option>
                        <option value="dakinspectie">Dakinspectie</option>
                        <option value="lekkage">Lekkage herstel</option>
                        <option value="offerte">Gratis offerte</option>
                        <option value="anders">Anders</option>
                      </select>
                    </div>

                    {/* Message */}
                    <div>
                      <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                        Bericht *
                      </label>
                      <textarea
                        id="message"
                        name="message"
                        value={formData.message}
                        onChange={handleInputChange}
                        required
                        rows={5}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-tappel-orange focus:border-transparent transition-colors resize-none"
                        placeholder="Beschrijf uw situatie, wensen en eventuele vragen..."
                      />
                    </div>

                    {/* Submit Button */}
                    <div className="pt-4">
                      {submitStatus === 'success' ? (
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="flex items-center gap-3 text-green-600 bg-green-50 p-4 rounded-lg"
                        >
                          <CheckCircle className="h-5 w-5" />
                          <span>Bedankt! We nemen binnen 24 uur contact op.</span>
                        </motion.div>
                      ) : (
                        <AnimatedButton
                          type="submit"
                          size="lg"
                          variant="primary"
                          disabled={isSubmitting}
                          className="w-full"
                          glow
                        >
                          {isSubmitting ? (
                            <>
                              <motion.div
                                animate={{ rotate: 360 }}
                                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                                className="w-5 h-5 border-2 border-white border-t-transparent rounded-full mr-2"
                              />
                              Versturen...
                            </>
                          ) : (
                            <>
                              <Send className="h-5 w-5 mr-2" />
                              GRATIS OFFERTE AANVRAGEN
                            </>
                          )}
                        </AnimatedButton>
                      )}
                    </div>
                  </form>
                </AnimatedCard>
            </div>

            {/* Contact Info */}
            <div>
              <AnimatedText variant="fade-up" delay={0.15}>
                <h2 className="text-2xl font-bold mb-8">Direct Contact</h2>
              </AnimatedText>

              <div className="space-y-6">
                {contactInfo.map((info, index) => (
                  <AnimatedText key={info.title} variant="fade-up" delay={0.2 + index * 0.02}>
                      <AnimatedCard
                        variant="hover-lift"
                        className={`p-6 bg-white rounded-xl shadow-md hover:shadow-lg transition-shadow duration-300 ${info.href ? 'cursor-pointer' : ''}`}
                        onClick={info.href ? () => window.open(info.href, '_self') : undefined}
                      >
                        <div className="flex items-start gap-4">
                          <div className="w-12 h-12 bg-tappel-orange/10 rounded-lg flex items-center justify-center text-tappel-orange">
                            {info.icon}
                          </div>
                          <div className="flex-1">
                            <h3 className="font-semibold text-gray-900 mb-1">{info.title}</h3>
                            <p className="text-lg font-medium text-tappel-orange mb-1">{info.value}</p>
                            <p className="text-sm text-gray-600">{info.description}</p>
                          </div>
                          {info.href && (
                            <div className="text-gray-400">
                              <div className="w-2 h-2 bg-tappel-orange rounded-full"></div>
                            </div>
                          )}
                        </div>
                      </AnimatedCard>
                  </AnimatedText>
                ))}
              </div>

              {/* Quick Contact */}
              <AnimatedText variant="fade-up" delay={0.3}>
                <div className="mt-8 p-6 bg-gradient-to-r from-tappel-orange to-orange-400 rounded-2xl text-white">
                  <h3 className="text-xl font-bold mb-3">Spoed? Bel Direct!</h3>
                  <p className="mb-4">Voor urgente lekkages en spoedgevallen zijn we direct bereikbaar.</p>
                  <AnimatedButton
                    asChild
                    variant="secondary"
                    size="lg"
                    className="bg-white text-tappel-orange hover:bg-gray-100"
                  >
                    <a href="tel:0621452819">
                      <Phone className="h-5 w-5 mr-2" />
                      06-21452819
                    </a>
                  </AnimatedButton>
                </div>
              </AnimatedText>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
