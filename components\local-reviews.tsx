interface Review {
  name: string;
  location: string;
  rating: number;
  text: string;
  project: string;
  date: string;
}

interface LocalReviewsProps {
  city: string;
}

export default function LocalReviews({ city }: LocalReviewsProps) {
  const getLocalReviews = (city: string): Review[] => {
    switch (city) {
      case "Rotterdam":
        return [
          {
            name: "<PERSON>",
            location: "Kop van Zuid, Rotterdam",
            rating: 5,
            text: "Uitstekende service! Ons kantoorpand kreeg een complete EPDM renovatie zonder overlast tijdens kantooruren. Team werkte professioneel en netjes.",
            project: "2.400m² EPDM renovatie kantoorgebouw",
            date: "November 2024"
          },
          {
            name: "<PERSON>",
            location: "Overschie, Rotterdam", 
            rating: 5,
            text: "Eindelijk geen lekkages meer! De jaren '60 flat kreeg nieuwe isolatie en dakbedekking. Energielabel verbeterd van E naar B. Stookkosten flink gedaald.",
            project: "Woonflat renovatie met isolatie",
            date: "Oktober 2024"
          },
          {
            name: "Technisch Manager ChemCorp",
            location: "Botlek, Rotterdam",
            rating: 5,
            text: "Specialistische kennis van industriële omgeving is uniek. Dak houdt perfect stand tegen chemische uitstoot en extreme weersomstandigheden.",
            project: "Industrieel complex renovatie",
            date: "September 2024"
          }
        ];
      case "Den Haag":
        return [
          {
            name: "Dr. Elisabeth van Houten",
            location: "Voorhout, Den Haag",
            rating: 5,
            text: "Monumentaal pand uit 1890 kreeg vakkundige dakrenovatie. Welstandsvergunning perfect geregeld, historische waarde behouden.",
            project: "Monumentale dakrenovatie herenhuis",
            date: "November 2024"
          },
          {
            name: "Facility Manager Ministerie",
            location: "Bezuidenhout, Den Haag",
            rating: 5,
            text: "Overheidsgebouw renovatie volgens strikte beveiligingseisen. Minimale overlast, perfecte planning, alle deadlines gehaald.",
            project: "Overheidsgebouw dakrenovatie",
            date: "Oktober 2024"
          },
          {
            name: "VvE Voorzitter",
            location: "Schilderswijk, Den Haag",
            rating: 5,
            text: "Sociale woningbouw uit jaren '70 kreeg nieuwe kans. Subsidie perfect geregeld, bewoners tevreden over communicatie en resultaat.",
            project: "Woonflat renovatie 48 appartementen",
            date: "September 2024"
          }
        ];
      case "Zoetermeer":
        return [
          {
            name: "Familie Bakker",
            location: "Rokkeveen, Zoetermeer",
            rating: 5,
            text: "Jaren '80 rijtjeshuis kreeg nieuwe dakbedekking en isolatie. SEEH subsidie geregeld, energierekening gehalveerd!",
            project: "Rijtjeshuis dakrenovatie",
            date: "November 2024"
          },
          {
            name: "Beheerder Wooncomplex",
            location: "Buytenwegh, Zoetermeer",
            rating: 5,
            text: "Modern wooncomplex uit 1995 kreeg EPDM renovatie. Bewoners zeer tevreden, geen overlast, perfecte planning.",
            project: "Wooncomplex 24 woningen",
            date: "Oktober 2024"
          },
          {
            name: "Bedrijfsleider TechPark",
            location: "Rokkeveen-Oost, Zoetermeer",
            rating: 5,
            text: "Bedrijfspand op bedrijventerrein kreeg bitumen renovatie. Snelle uitvoering, concurrerende prijs, uitstekend resultaat.",
            project: "Bedrijfspand 1.200m²",
            date: "September 2024"
          }
        ];
      case "Amsterdam":
        return [
          {
            name: "Architect Jan Visser",
            location: "Jordaan, Amsterdam",
            rating: 5,
            text: "Groendak installatie op grachtenpand perfect uitgevoerd. Klimaatadaptatie en esthetiek perfect gecombineerd.",
            project: "Extensief groendak grachtenpand",
            date: "November 2024"
          },
          {
            name: "VvE De Plantage",
            location: "Centrum, Amsterdam",
            rating: 5,
            text: "Monumentaal pand uit 1880 kreeg EPDM renovatie. Welstandscommissie tevreden, bewoners ook. Uitstekende communicatie.",
            project: "Monumentaal pand renovatie",
            date: "Oktober 2024"
          },
          {
            name: "Startup Incubator",
            location: "Noord, Amsterdam",
            rating: 5,
            text: "Modern kantoorpand kreeg groendak met zonnepanelen. Innovatieve oplossing, perfect uitgevoerd binnen budget.",
            project: "Groendak + zonnepanelen combinatie",
            date: "September 2024"
          }
        ];
      case "Utrecht":
        return [
          {
            name: "Prof. Dr. van der Berg",
            location: "Binnenstad, Utrecht",
            rating: 5,
            text: "Universiteitsgebouw renovatie tijdens zomervakantie. Geen overlast, perfecte planning, historische waarde behouden.",
            project: "Universiteitsgebouw dakrenovatie",
            date: "Augustus 2024"
          },
          {
            name: "Bewonerscommissie",
            location: "Leidsche Rijn, Utrecht",
            rating: 5,
            text: "Moderne woonwijk uit 2005 kreeg EPDM renovatie. Alle 24 woningen in één week klaar. Bewoners zeer tevreden.",
            project: "Woonwijk 24 woningen",
            date: "Oktober 2024"
          },
          {
            name: "Bio-tech Bedrijf",
            location: "Science Park, Utrecht",
            rating: 5,
            text: "Laboratorium dak met speciale eisen. Cleanroom omgeving behouden, geen contaminatie. Professioneel werk.",
            project: "Laboratorium dakrenovatie",
            date: "September 2024"
          }
        ];
      case "Haarlem":
        return [
          {
            name: "Museum Directeur",
            location: "Centrum, Haarlem",
            rating: 5,
            text: "Cultureel erfgoed gebouw kreeg zorgvuldige dakrenovatie. Historische waarde behouden, moderne technieken toegepast.",
            project: "Museum dakrenovatie",
            date: "November 2024"
          },
          {
            name: "Familie Jansen",
            location: "Schalkwijk, Haarlem",
            rating: 5,
            text: "Jaren '70 rijtjeshuis kreeg nieuwe EPDM bedekking. Energielabel verbeterd, geen lekkages meer. Top service!",
            project: "Rijtjeshuis renovatie",
            date: "Oktober 2024"
          },
          {
            name: "Woningcorporatie Haarlem",
            location: "Noord, Haarlem",
            rating: 5,
            text: "36 sociale huurwoningen gerenoveerd. Planning perfect, bewoners goed geïnformeerd. Uitstekende samenwerking.",
            project: "Sociale woningbouw 36 woningen",
            date: "September 2024"
          }
        ];
      default:
        return [];
    }
  };

  const reviews = getLocalReviews(city);
  const averageRating = reviews.length > 0 ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length : 5;

  if (reviews.length === 0) return null;

  return (
    <div className="bg-white p-8 rounded-lg shadow-md">
      <div className="text-center mb-8">
        <h3 className="text-2xl font-bold mb-4">Wat Klanten Zeggen in {city}</h3>
        <div className="flex items-center justify-center gap-2 mb-2">
          <div className="flex">
            {[...Array(5)].map((_, i) => (
              <span key={i} className={`text-2xl ${i < averageRating ? 'text-yellow-400' : 'text-gray-300'}`}>
                ⭐
              </span>
            ))}
          </div>
          <span className="text-lg font-semibold">{averageRating.toFixed(1)}</span>
        </div>
        <p className="text-gray-600">Gebaseerd op {reviews.length} recente projecten in {city}</p>
      </div>

      <div className="grid md:grid-cols-1 lg:grid-cols-3 gap-6">
        {reviews.map((review, index) => (
          <div key={index} className="border border-gray-200 p-6 rounded-lg">
            <div className="flex items-center mb-3">
              <div className="flex">
                {[...Array(review.rating)].map((_, i) => (
                  <span key={i} className="text-yellow-400">⭐</span>
                ))}
              </div>
              <span className="ml-2 text-sm text-gray-500">{review.date}</span>
            </div>
            
            <p className="text-gray-700 mb-4 italic">"{review.text}"</p>
            
            <div className="border-t pt-3">
              <p className="font-semibold text-sm">{review.name}</p>
              <p className="text-sm text-gray-600">{review.location}</p>
              <p className="text-xs text-tappel-orange mt-1">{review.project}</p>
            </div>
          </div>
        ))}
      </div>

      <div className="text-center mt-8">
        <div className="bg-green-50 p-4 rounded-lg inline-block">
          <p className="text-green-700 font-semibold">
            🏆 100% Klanttevredenheid in {city}
          </p>
          <p className="text-green-600 text-sm">
            Alle projecten afgerond binnen tijd en budget
          </p>
        </div>
      </div>
    </div>
  );
}
