import Image from "next/image"
import Link from "next/link"
import { But<PERSON> } from "../../../components/ui/button"
import { ArrowLeft, Calendar, User, Tag, Share2, Facebook, Twitter, Linkedin, ArrowRight } from "lucide-react"
import type { Metadata } from "next"
import { notFound } from "next/navigation"
import { blogPosts } from "../../../lib/blog-data"

type Props = {
  params: { slug: string }
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const post = blogPosts.find((post) => post.slug === params.slug)

  if (!post) {
    return {
      title: "Artikel niet gevonden | Tappel Dakwerken Blog",
      description: "Het opgevraagde blogartikel kon niet worden gevonden.",
    }
  }

  return {
    title: `${post.title} | Tappel Dakwerken Blog`,
    description: post.excerpt,
    keywords: [...post.categories, ...post.keywords].join(", "),
    alternates: {
      canonical: `/blog/${params.slug}`,
    },
    openGraph: {
      title: `${post.title} | Tappel Dakwerken Blog`,
      description: post.excerpt,
      url: `https://tappeldakwerken.nl/blog/${params.slug}`,
      type: "article",
      publishedTime: post.publishedAt,
      authors: [post.author],
      images: [
        {
          url: post.coverImage,
          width: 1200,
          height: 630,
          alt: post.title,
        },
      ],
    },
  }
}

export async function generateStaticParams() {
  return blogPosts.map((post) => ({
    slug: post.slug,
  }))
}

export default function BlogPostPage({ params }: Props) {
  const post = blogPosts.find((post) => post.slug === params.slug)

  if (!post) {
    notFound()
  }

  // Find related posts (same category first, then other posts)
  const relatedByCategory = blogPosts
    .filter((p) => p.slug !== params.slug && p.categories.some((cat) => post.categories.includes(cat)))

  const otherPosts = blogPosts
    .filter((p) => p.slug !== params.slug && !p.categories.some((cat) => post.categories.includes(cat)))

  const relatedPosts = [...relatedByCategory, ...otherPosts].slice(0, 3)

  return (
    <>
      {/* Hero Section */}
      <section className="bg-gray-100 py-12 md:py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <Link href="/blog" className="inline-flex items-center text-tappel-orange hover:underline mb-6">
              <ArrowLeft className="mr-2 h-4 w-4" /> Terug naar alle artikelen
            </Link>

            <div className="flex flex-wrap gap-2 mb-4">
              {post.categories.map((category) => (
                <span
                  key={category}
                  className="inline-flex items-center text-xs font-medium bg-tappel-orange/10 text-tappel-orange px-2.5 py-0.5 rounded-full"
                >
                  <Tag className="mr-1 h-3 w-3" />
                  {category}
                </span>
              ))}
            </div>

            <h1 className="text-3xl md:text-5xl font-bold mb-6">{post.title}</h1>

            <div className="flex flex-wrap items-center text-gray-600 gap-4 mb-6">
              <div className="flex items-center">
                <User className="mr-1 h-4 w-4" />
                <span>{post.author}</span>
              </div>
              <div className="flex items-center">
                <Calendar className="mr-1 h-4 w-4" />
                <span>{post.date}</span>
              </div>
              <div className="flex items-center">
                <span className="text-sm">{post.readingTime} min leestijd</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Image */}
      <section className="py-8 md:py-12">
        <div className="container mx-auto px-4">
          <div className="relative aspect-[16/9] max-w-4xl mx-auto rounded-lg overflow-hidden shadow-lg">
            <Image
              src={post.coverImage || "/placeholder.svg"}
              alt={post.title}
              fill
              className="object-cover"
              priority
            />
          </div>
        </div>
      </section>

      {/* Article Content */}
      <section className="py-12 md:py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto">
            <article className="prose prose-lg max-w-none">
              {post.content.map((section, index) => (
                <div key={index}>
                  {section.type === "paragraph" && <p>{section.content}</p>}
                  {section.type === "heading" && <h2 className="text-2xl font-bold mt-8 mb-4">{section.content}</h2>}
                  {section.type === "subheading" && <h3 className="text-xl font-bold mt-6 mb-3">{section.content}</h3>}
                  {section.type === "list" && (
                    <ul className="my-4 space-y-2">
                      {section.items.map((item, i) => (
                        <li key={i} className="flex items-start">
                          <span className="text-tappel-orange mr-2">•</span>
                          <span>{item}</span>
                        </li>
                      ))}
                    </ul>
                  )}
                  {section.type === "image" && (
                    <div className="my-8">
                      <div className="relative aspect-[16/9] rounded-lg overflow-hidden">
                        <Image
                          src={section.src || "/placeholder.svg"}
                          alt={section.alt || ""}
                          fill
                          className="object-cover"
                        />
                      </div>
                      {section.caption && <p className="text-sm text-gray-500 text-center mt-2">{section.caption}</p>}
                    </div>
                  )}
                  {section.type === "quote" && (
                    <blockquote className="border-l-4 border-rtdak-orange pl-4 italic my-6">
                      {section.content}
                      {section.author && <footer className="text-sm mt-2">— {section.author}</footer>}
                    </blockquote>
                  )}
                </div>
              ))}
            </article>

            {/* Tags */}
            <div className="mt-12 pt-6 border-t">
              <div className="flex flex-wrap gap-2">
                {post.keywords.map((keyword) => (
                  <span key={keyword} className="inline-block bg-gray-100 text-gray-800 text-sm px-3 py-1 rounded-full">
                    {keyword}
                  </span>
                ))}
              </div>
            </div>

            {/* Share */}
            <div className="mt-8">
              <h3 className="text-lg font-bold mb-4 flex items-center">
                <Share2 className="mr-2 h-5 w-5" /> Deel dit artikel
              </h3>
              <div className="flex space-x-4">
                <a
                  href={`https://www.facebook.com/sharer/sharer.php?u=https://tappeldakwerken.nl/blog/${post.slug}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-500 hover:text-blue-600 transition-colors"
                >
                  <Facebook size={24} />
                  <span className="sr-only">Deel op Facebook</span>
                </a>
                <a
                  href={`https://twitter.com/intent/tweet?url=https://tappeldakwerken.nl/blog/${post.slug}&text=${post.title}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-500 hover:text-blue-400 transition-colors"
                >
                  <Twitter size={24} />
                  <span className="sr-only">Deel op Twitter</span>
                </a>
                <a
                  href={`https://www.linkedin.com/shareArticle?mini=true&url=https://tappeldakwerken.nl/blog/${post.slug}&title=${post.title}&summary=${post.excerpt}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-500 hover:text-blue-700 transition-colors"
                >
                  <Linkedin size={24} />
                  <span className="sr-only">Deel op LinkedIn</span>
                </a>
              </div>
            </div>

            {/* Author Bio */}
            <div className="mt-12 p-6 bg-gray-50 rounded-lg">
              <div className="flex items-center mb-4">
                <div className="relative w-16 h-16 rounded-full overflow-hidden mr-4">
                  <Image
                    src="/placeholder.svg?height=100&width=100"
                    alt={`Foto van ${post.author}`}
                    fill
                    className="object-cover"
                  />
                </div>
                <div>
                  <h3 className="font-bold text-lg">{post.author}</h3>
                  <p className="text-gray-600">Dakspecialist bij Tappel Dakwerken</p>
                </div>
              </div>
              <p className="text-gray-700">
                Met meer dan 15 jaar ervaring in de dakbranche deelt onze specialist graag kennis en advies over alles
                wat met daken te maken heeft. Specialisaties: dakdoorvoeren, zonnepanelen en duurzame dakoplossingen.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Related Articles */}
      {relatedPosts.length > 0 && (
        <section className="py-12 md:py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="max-w-5xl mx-auto">
              <h2 className="text-3xl font-bold mb-8 text-center">Gerelateerde Artikelen</h2>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                {relatedPosts.map((relatedPost) => (
                  <div
                    key={relatedPost.slug}
                    className="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow"
                  >
                    <Link href={`/blog/${relatedPost.slug}`} className="block relative h-48 overflow-hidden">
                      <Image
                        src={relatedPost.coverImage || "/placeholder.svg"}
                        alt={relatedPost.title}
                        fill
                        className="object-cover transition-transform hover:scale-105 duration-300"
                      />
                    </Link>
                    <div className="p-6">
                      <Link href={`/blog/${relatedPost.slug}`}>
                        <h3 className="text-lg font-bold mb-2 hover:text-rtdak-orange transition-colors">
                          {relatedPost.title}
                        </h3>
                      </Link>
                      <p className="text-gray-600 text-sm mb-4">{relatedPost.excerpt}</p>
                      <Button asChild variant="outline" className="group w-full">
                        <Link href={`/blog/${relatedPost.slug}`}>
                          Lees meer{" "}
                          <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                        </Link>
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>
      )}

      {/* CTA Section */}
      <section className="py-16 md:py-24 bg-tappel-orange text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">Heeft u een vraag over uw dak?</h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            Neem contact met ons op voor persoonlijk advies of een vrijblijvende offerte. Onze dakspecialisten staan
            voor u klaar!
          </p>
          <Button
            asChild
            size="lg"
            variant="outline"
            className="bg-white text-tappel-orange hover:bg-gray-100 border-white"
          >
            <Link href="/contact">CONTACT OPNEMEN</Link>
          </Button>
        </div>
      </section>
    </>
  )
}

