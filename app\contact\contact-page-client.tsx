"use client"
import Link from "next/link"
import { But<PERSON> } from "../../components/ui/button"
import { Input } from "../../components/ui/input"
import { Textarea } from "../../components/ui/textarea"
import { Phone, Mail, MapPin, Clock } from "lucide-react"

import { useState, useEffect } from "react"
import { useInvisibleRecaptcha, InvisibleReCaptchaContainer } from "../../components/recaptcha"

declare global {
  interface Window {
    grecaptcha: any;
  }
}

export default function ContactPageClient() {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [statusMessage, setStatusMessage] = useState('');
  const [recaptchaToken, setRecaptchaToken] = useState<string | null>(null);

  // Invisible reCAPTCHA hook
  const { executeRecaptcha, resetRecaptcha } = useInvisibleRecaptcha();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus('idle');

    // Execute invisible reCAPTCHA
    executeRecaptcha(async (token: string) => {
      if (!token) {
        setSubmitStatus('error');
        setStatusMessage('reCAPTCHA verificatie mislukt. Probeer opnieuw.');
        setIsSubmitting(false);
        return;
      }

      await submitForm(token);
    });
  };

  const submitForm = async (recaptchaToken: string) => {

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          recaptchaToken: recaptchaToken
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setSubmitStatus('success');
        setStatusMessage(data.message);
        setFormData({
          firstName: '',
          lastName: '',
          email: '',
          phone: '',
          subject: '',
          message: ''
        });
        // Reset invisible reCAPTCHA
        resetRecaptcha();
      } else {
        setSubmitStatus('error');
        setStatusMessage(data.error || 'Er is een fout opgetreden.');
        // Reset reCAPTCHA on error
        resetRecaptcha();
      }
    } catch (error) {
      setSubmitStatus('error');
      setStatusMessage('Er is een fout opgetreden bij het verzenden van uw bericht.');
      // Reset reCAPTCHA on error
      resetRecaptcha();
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      {/* Hero Section */}
      <section className="bg-gray-100 py-12 md:py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Contact</h1>
            <p className="text-xl text-gray-700">
              Heeft u vragen of wilt u een afspraak inplannen?
            </p>
            <p className="text-xl text-gray-700">
              Neem gerust contact met ons op. Wij denken graag met u mee.
            </p>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 md:py-24">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div className="bg-white p-8 rounded-lg shadow-md">
              <h2 className="text-2xl font-bold mb-6">Stuur ons een bericht</h2>

              {submitStatus === 'success' && (
                <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                  <p className="text-green-800">{statusMessage}</p>
                </div>
              )}

              {submitStatus === 'error' && (
                <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-red-800">{statusMessage}</p>
                </div>
              )}

              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label htmlFor="firstName" className="text-sm font-medium">
                      Voornaam *
                    </label>
                    <Input
                      id="firstName"
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleInputChange}
                      placeholder="Voornaam"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="lastName" className="text-sm font-medium">
                      Achternaam *
                    </label>
                    <Input
                      id="lastName"
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleInputChange}
                      placeholder="Achternaam"
                      required
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <label htmlFor="email" className="text-sm font-medium">
                    E-mailadres *
                  </label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <label htmlFor="phone" className="text-sm font-medium">
                    Telefoonnummer
                  </label>
                  <Input
                    id="phone"
                    name="phone"
                    type="tel"
                    value={formData.phone}
                    onChange={handleInputChange}
                    placeholder="Telefoonnummer"
                  />
                </div>
                <div className="space-y-2">
                  <label htmlFor="subject" className="text-sm font-medium">
                    Onderwerp *
                  </label>
                  <Input
                    id="subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleInputChange}
                    placeholder="Onderwerp van uw bericht"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <label htmlFor="message" className="text-sm font-medium">
                    Bericht *
                  </label>
                  <Textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    placeholder="Uw bericht aan ons"
                    rows={5}
                    required
                  />
                </div>

                {/* Invisible reCAPTCHA - no visible widget needed */}

                <Button
                  type="submit"
                  className="w-full"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'BEZIG MET VERSTUREN...' : 'VERSTUREN'}
                </Button>
              </form>
            </div>

            {/* Contact Info */}
            <div className="space-y-8">
              <div>
                <h2 className="text-2xl font-bold mb-6">Contactgegevens</h2>
                <div className="space-y-4">
                  <div className="flex items-start">
                    <Phone className="mr-3 h-5 w-5 text-tappel-orange flex-shrink-0 mt-0.5" />
                    <div>
                      <h3 className="font-medium">Telefoon</h3>
                      <p className="text-gray-600">06-21452819</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <Mail className="mr-3 h-5 w-5 text-tappel-orange flex-shrink-0 mt-0.5" />
                    <div>
                      <h3 className="font-medium">E-mail</h3>
                      <p className="text-gray-600"><EMAIL></p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <MapPin className="mr-3 h-5 w-5 text-tappel-orange flex-shrink-0 mt-0.5" />
                    <div>
                      <h3 className="font-medium">Adres</h3>
                      <p className="text-gray-600">Trondheimpad 13</p>
                      <p className="text-gray-600">3067DM Rotterdam</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <Clock className="mr-3 h-5 w-5 text-tappel-orange flex-shrink-0 mt-0.5" />
                    <div>
                      <h3 className="font-medium">Openingstijden</h3>
                      <p className="text-gray-600">Maandag - Vrijdag: 08:00 - 17:00</p>
                      <p className="text-gray-600">Zaterdag - Zondag: Gesloten</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 p-6 rounded-lg">
                <h3 className="text-xl font-bold mb-4">Spoedgevallen</h3>
                <p className="text-gray-700 mb-4">
                  Heeft u een acute lekkage of een ander spoedeisend dakprobleem? Neem dan direct telefonisch contact
                  met ons op.
                </p>
                <div className="flex items-center text-tappel-orange font-bold text-lg">
                  <Phone className="mr-2 h-5 w-5" />
                  <span>06-21452819</span>
                </div>
              </div>

              <div className="bg-gray-50 p-6 rounded-lg">
                <h3 className="text-xl font-bold mb-4">Offerte Aanvragen</h3>
                <p className="text-gray-700">
                  Wilt u een vrijblijvende offerte voor uw dakproject? Vul het contactformulier in of neem telefonisch
                  contact met ons op.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Map Section */}
      <section className="py-8 md:py-12">
        <div className="container mx-auto px-4">
          <div className="aspect-[16/9] w-full rounded-lg overflow-hidden shadow-md">
            <iframe
              src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2436.5969246060417!2d4.8986305!3d52.3702157!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zNTLCsDIyJzEyLjkiTiA0wrA1Myc1NS4xIkU!5e0!3m2!1snl!2snl!4v1615293887287!5m2!1snl!2snl"
              width="100%"
              height="100%"
              style={{ border: 0 }}
              allowFullScreen
              loading="lazy"
              title="Tappel Dakwerken locatie"
            ></iframe>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 md:py-24 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-3xl font-bold mb-8 text-center">Veelgestelde Vragen</h2>

            <div className="space-y-6">
              <div className="bg-white p-6 rounded-lg shadow-sm">
                <h3 className="text-xl font-bold mb-2">Hoe lang duurt de installatie van een dakdoorvoer?</h3>
                <p className="text-gray-700">
                  De installatie van een dakdoorvoer duurt gemiddeld 2-3 uur, afhankelijk van het type dak en de
                  specifieke situatie. We streven ernaar om de werkzaamheden zo efficiënt mogelijk uit te voeren met
                  minimale overlast.
                </p>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm">
                <h3 className="text-xl font-bold mb-2">Zijn jullie dakdoorvoeren geschikt voor alle daktypen?</h3>
                <p className="text-gray-700">
                  Ja, onze dakdoorvoeren zijn geschikt voor zowel platte als licht hellende daken. We hebben oplossingen
                  voor verschillende dakbedekkingen, zoals bitumen, EPDM, PVC en zink.
                </p>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm">
                <h3 className="text-xl font-bold mb-2">Geven jullie garantie op de werkzaamheden?</h3>
                <p className="text-gray-700">
                  Absoluut! We geven 10 jaar garantie op al onze dakwerkzaamheden en 5 jaar op de installatie van
                  dakdoorvoeren. Daarnaast gelden de fabrieksgaranties op de gebruikte materialen.
                </p>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm">
                <h3 className="text-xl font-bold mb-2">Werken jullie samen met zonnepanelen installateurs?</h3>
                <p className="text-gray-700">
                  Ja, we werken samen met verschillende zonnepanelen installateurs. We kunnen de dakdoorvoeren en
                  montage van ankers verzorgen voorafgaand aan de installatie van de zonnepanelen, of in samenwerking
                  met uw eigen installateur.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 md:py-24 bg-tappel-orange text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">Klaar voor een duurzaam en betrouwbaar dak?</h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            Neem vandaag nog contact met ons op voor een vrijblijvende offerte of adviesgesprek. Onze experts staan voor
            je klaar!
          </p>
          <Button
            asChild
            size="lg"
            variant="outline"
            className="bg-white text-tappel-orange hover:bg-gray-100 border-white"
          >
            <Link href="#" onClick={() => window.scrollTo(0, 0)}>
              NAAR CONTACTFORMULIER
            </Link>
          </Button>
        </div>
      </section>

      {/* Invisible reCAPTCHA container */}
      <InvisibleReCaptchaContainer />
    </>
  )
}

