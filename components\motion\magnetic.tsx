"use client"

import { motion, useMotionValue, useSpring, useTransform } from "framer-motion"
import { useRef, useState } from "react"
import { cn } from "@/lib/utils"

interface MagneticProps {
  children: React.ReactNode
  className?: string
  strength?: number
  range?: number
  springConfig?: {
    stiffness: number
    damping: number
    mass?: number
  }
  disabled?: boolean
}

export function Magnetic({
  children,
  className,
  strength = 0.3,
  range = 100,
  springConfig = { stiffness: 300, damping: 30 },
  disabled = false
}: MagneticProps) {
  const ref = useRef<HTMLDivElement>(null)
  const [isHovered, setIsHovered] = useState(false)
  
  const mouseX = useMotionValue(0)
  const mouseY = useMotionValue(0)
  
  const x = useSpring(mouseX, springConfig)
  const y = useSpring(mouseY, springConfig)
  
  const handleMouseMove = (e: React.MouseEvent) => {
    if (disabled || !ref.current) return
    
    const rect = ref.current.getBoundingClientRect()
    const centerX = rect.left + rect.width / 2
    const centerY = rect.top + rect.height / 2
    
    const distanceX = e.clientX - centerX
    const distanceY = e.clientY - centerY
    const distance = Math.sqrt(distanceX ** 2 + distanceY ** 2)
    
    if (distance < range) {
      const factor = (range - distance) / range
      mouseX.set(distanceX * strength * factor)
      mouseY.set(distanceY * strength * factor)
    } else {
      mouseX.set(0)
      mouseY.set(0)
    }
  }
  
  const handleMouseLeave = () => {
    setIsHovered(false)
    mouseX.set(0)
    mouseY.set(0)
  }
  
  const handleMouseEnter = () => {
    setIsHovered(true)
  }
  
  return (
    <motion.div
      ref={ref}
      className={cn("relative", className)}
      style={{ x, y }}
      onMouseMove={handleMouseMove}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {children}
    </motion.div>
  )
}

// Specialized Magnetic component for navigation items
interface MagneticNavItemProps {
  children: React.ReactNode
  className?: string
  href?: string
  isActive?: boolean
  onClick?: () => void
}

export function MagneticNavItem({
  children,
  className,
  href,
  isActive,
  onClick
}: MagneticNavItemProps) {
  return (
    <Magnetic strength={0.4} range={80}>
      <motion.div
        className={cn(
          "relative px-4 py-2 rounded-lg transition-colors cursor-pointer",
          isActive 
            ? "text-tappel-orange bg-tappel-orange/10" 
            : "text-gray-700 hover:text-tappel-orange hover:bg-gray-50",
          className
        )}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={onClick}
      >
        {children}
        
        {/* Active indicator */}
        {isActive && (
          <motion.div
            className="absolute bottom-0 left-1/2 w-1 h-1 bg-tappel-orange rounded-full"
            layoutId="activeIndicator"
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            style={{ x: "-50%" }}
          />
        )}
      </motion.div>
    </Magnetic>
  )
}

// Magnetic button with enhanced effects
interface MagneticButtonProps {
  children: React.ReactNode
  className?: string
  variant?: "primary" | "secondary" | "ghost"
  size?: "sm" | "md" | "lg"
  onClick?: () => void
  disabled?: boolean
}

export function MagneticButton({
  children,
  className,
  variant = "primary",
  size = "md",
  onClick,
  disabled
}: MagneticButtonProps) {
  const variantClasses = {
    primary: "bg-tappel-orange text-white hover:bg-tappel-orange/90",
    secondary: "bg-tappel-blue text-white hover:bg-tappel-blue/90",
    ghost: "text-tappel-orange hover:bg-tappel-orange/10"
  }
  
  const sizeClasses = {
    sm: "px-3 py-1.5 text-sm",
    md: "px-4 py-2",
    lg: "px-6 py-3 text-lg"
  }
  
  return (
    <Magnetic strength={0.5} range={60} disabled={disabled}>
      <motion.button
        className={cn(
          "relative rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-tappel-orange focus:ring-offset-2",
          variantClasses[variant],
          sizeClasses[size],
          disabled && "opacity-50 cursor-not-allowed",
          className
        )}
        whileHover={disabled ? undefined : { 
          scale: 1.05,
          boxShadow: "0 8px 25px rgba(255, 107, 53, 0.2)"
        }}
        whileTap={disabled ? undefined : { scale: 0.95 }}
        onClick={onClick}
        disabled={disabled}
      >
        {children}
        
        {/* Glow effect */}
        <motion.div
          className="absolute inset-0 rounded-lg bg-gradient-to-r from-tappel-orange to-orange-400 blur-lg opacity-0"
          whileHover={{ opacity: 0.3 }}
          style={{ zIndex: -1 }}
        />
      </motion.button>
    </Magnetic>
  )
}

// Magnetic card wrapper
interface MagneticCardProps {
  children: React.ReactNode
  className?: string
  intensity?: "subtle" | "medium" | "strong"
}

export function MagneticCard({
  children,
  className,
  intensity = "medium"
}: MagneticCardProps) {
  const strengthMap = {
    subtle: 0.2,
    medium: 0.4,
    strong: 0.6
  }
  
  const rangeMap = {
    subtle: 60,
    medium: 100,
    strong: 140
  }
  
  return (
    <Magnetic 
      strength={strengthMap[intensity]} 
      range={rangeMap[intensity]}
      className={className}
    >
      <motion.div
        className="w-full h-full"
        whileHover={{ 
          scale: 1.02,
          rotateX: 5,
          rotateY: 5,
          transition: { type: "spring", stiffness: 300, damping: 20 }
        }}
        style={{ transformStyle: "preserve-3d" }}
      >
        {children}
      </motion.div>
    </Magnetic>
  )
}
