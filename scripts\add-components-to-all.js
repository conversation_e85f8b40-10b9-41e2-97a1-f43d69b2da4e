const fs = require('fs');
const path = require('path');

const cities = [
  'amsterdam', 'utrecht', 'haarlem', 'leiden', 'delft', 'alkmaar', 
  'gouda', 'dordrecht', 'amersfoort', 'amstelveen', 'hilversum',
  'nieuwegein', 'veenendaal', 'westland', 'zaanstad', 'zeist',
  'haarlemmermeer'
];

function addComponentsToCity(cityName) {
  const filePath = path.join(__dirname, '..', 'app', `dakrenovatie-${cityName}`, 'page.tsx');
  
  if (!fs.existsSync(filePath)) {
    console.log(`Bestand niet gevonden: ${filePath}`);
    return;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  
  // Voeg imports toe als ze er nog niet zijn
  if (!content.includes('LocalReviews')) {
    content = content.replace(
      'import { GlowEffect, Spotlight } from "@/components/motion/glow-effects";',
      `import { GlowEffect, Spotlight } from "@/components/motion/glow-effects";
import LocalReviews from "@/components/local-reviews";
import LocalContactInfo from "@/components/local-contact-info";
import CommercialBenefits from "@/components/commercial-benefits";`
    );
  }

  // Voeg LocalReviews sectie toe voor het einde
  if (!content.includes('LocalReviews city=')) {
    content = content.replace(
      /(\s+)<\/section>\s+<\/div>\s+<\/\w+>\s*\);/,
      `$1</section>

      {/* Reviews Section */}
      <section className="py-16 md:py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <LocalReviews city="${cityName.charAt(0).toUpperCase() + cityName.slice(1)}" />
          </div>
        </div>
      </section>

      {/* Commercial Benefits */}
      <section className="py-16 md:py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <CommercialBenefits city="${cityName.charAt(0).toUpperCase() + cityName.slice(1)}" />
          </div>
        </div>
      </section>

      {/* Local Contact */}
      <section className="py-16 md:py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <LocalContactInfo 
              city="${cityName.charAt(0).toUpperCase() + cityName.slice(1)}"
              localPhone="06-21452819"
              emergencyAvailable={true}
            />
          </div>
        </div>
      </section>
    </div>
  );`
    );
  }

  // Voeg "near me" teksten toe
  content = content.replace(
    /Als (.*?) specialist/g,
    `Als dakdekker in ${cityName.charAt(0).toUpperCase() + cityName.slice(1)} en $1 specialist`
  );

  content = content.replace(
    /Wij kennen (.*?) en/g,
    `Zoekt u een plat dak specialist in de buurt? Wij kennen $1 en`
  );

  fs.writeFileSync(filePath, content);
  console.log(`Componenten toegevoegd aan: ${cityName}`);
}

// Voeg componenten toe aan alle steden
cities.forEach(cityName => {
  addComponentsToCity(cityName);
});

console.log('Alle componenten zijn toegevoegd!');
