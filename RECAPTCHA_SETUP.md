# reCAPTCHA Setup Instructies

## Probleem
De huidige reCAPTCHA site key is niet geldig voor uw domein. U ziet de fout: "FOUT voor site-eigenaar: ongeldig sleuteltype"

## Oplossing: Nieuwe reCAPTCHA Keys Aanmaken

### Stap 1: Google reCAPTCHA Console
1. Ga naar: https://www.google.com/recaptcha/admin/create
2. Log in met uw Google account

### Stap 2: Nieuwe Site Registreren
1. **Label**: "Tappel Dakwerken Website"
2. **reCAPTCHA type**: Selecteer "reCAPTCHA v2" → "I'm not a robot" Checkbox
3. **Domains**: Voeg de volgende domeinen toe:
   - `localhost` (voor development)
   - `tappeldakwerken.nl` (uw productie domein)
   - `www.tappeldakwerken.nl`
   - Eventuele andere domeinen waar de website draait

### Stap 3: Keys Kopiëren
Na het aanmaken krijgt u twee keys:
- **Site Key** (pub<PERSON><PERSON>, begint met 6Lc...)
- **Secret Key** (privé, begint met 6Lc...)

### Stap 4: Environment Variables Instellen

#### Voor Development (lokaal)
1. Kopieer `.env.example` naar `.env.local`
2. Vul de keys in:
```
NEXT_PUBLIC_RECAPTCHA_SITE_KEY=6LcUW_SITE_KEY_HIER
RECAPTCHA_SECRET_KEY=6LcUW_SECRET_KEY_HIER
```

#### Voor Production (Vercel)
1. Ga naar uw Vercel project dashboard
2. Ga naar "Settings" > "Environment Variables"
3. Voeg toe:
   - `NEXT_PUBLIC_RECAPTCHA_SITE_KEY`: Uw site key
   - `RECAPTCHA_SECRET_KEY`: Uw secret key

### Stap 5: Website Opnieuw Starten
```bash
# Docker development
docker-compose restart nextjs-dev

# Of lokaal
npm run dev
```

## Tijdelijke Oplossing
Voor nu is reCAPTCHA uitgeschakeld in development modus. Het contactformulier werkt zonder reCAPTCHA op localhost.

In productie moet u wel geldige reCAPTCHA keys configureren.

## Verificatie
Na het instellen van de keys:
1. Ga naar het contactformulier
2. De reCAPTCHA checkbox zou zichtbaar moeten zijn
3. Test het formulier door een bericht te versturen

## Troubleshooting
- **"Invalid site key"**: Controleer of de site key correct is en het domein is toegevoegd
- **"Invalid secret key"**: Controleer de secret key in de environment variables
- **reCAPTCHA niet zichtbaar**: Controleer of `NEXT_PUBLIC_RECAPTCHA_SITE_KEY` is ingesteld
