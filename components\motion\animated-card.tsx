"use client"

import { motion, type Variants } from "framer-motion"
import { forwardRef } from "react"
import { cn } from "@/lib/utils"
import Image from "next/image"
import Link from "next/link"
import { ArrowRight } from "lucide-react"

interface AnimatedCardProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  variant?: "default" | "hover-lift" | "hover-glow" | "tilt" | "magnetic"
  intensity?: "subtle" | "medium" | "strong"
  asChild?: boolean
  href?: string
}

const cardVariants: Record<string, Variants> = {
  default: {
    initial: { 
      scale: 1,
      boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)"
    },
    hover: { 
      scale: 1.02,
      boxShadow: "0 8px 25px rgba(0, 0, 0, 0.15)",
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 25
      }
    }
  },
  "hover-lift": {
    initial: { 
      y: 0,
      boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)"
    },
    hover: { 
      y: -8,
      boxShadow: "0 20px 40px rgba(0, 0, 0, 0.15)",
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 20
      }
    }
  },
  "hover-glow": {
    initial: { 
      scale: 1,
      boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)"
    },
    hover: { 
      scale: 1.03,
      boxShadow: "0 8px 30px rgba(255, 107, 53, 0.2)",
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 25
      }
    }
  },
  tilt: {
    initial: { 
      rotateX: 0,
      rotateY: 0,
      scale: 1
    },
    hover: { 
      rotateX: 5,
      rotateY: 5,
      scale: 1.02,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 20
      }
    }
  },
  magnetic: {
    initial: { 
      scale: 1,
      x: 0,
      y: 0
    },
    hover: { 
      scale: 1.05,
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 25
      }
    }
  }
}

const AnimatedCard = forwardRef<HTMLDivElement, AnimatedCardProps>(
  ({ 
    className, 
    children, 
    variant = "default",
    intensity = "medium",
    ...props 
  }, ref) => {
    
    const variants = cardVariants[variant]
    
    // Adjust intensity
    const intensityMultiplier = {
      subtle: 0.5,
      medium: 1,
      strong: 1.5
    }[intensity]

    // Apply intensity to hover effects
    if (variants.hover && typeof variants.hover === 'object') {
      if ('scale' in variants.hover && typeof variants.hover.scale === 'number') {
        const baseScale = variants.hover.scale as number
        variants.hover.scale = 1 + (baseScale - 1) * intensityMultiplier
      }
      if ('y' in variants.hover && typeof variants.hover.y === 'number') {
        const baseY = variants.hover.y as number
        variants.hover.y = baseY * intensityMultiplier
      }
    }

    return (
      <motion.div
        ref={ref}
        className={cn(
          "relative overflow-hidden rounded-lg bg-white border border-gray-200 cursor-pointer",
          className
        )}
        variants={variants}
        initial="initial"
        whileHover="hover"
        whileTap={{ scale: 0.98 }}
        {...props}
      >
        {children}
      </motion.div>
    )
  }
)

AnimatedCard.displayName = "AnimatedCard"

// Specialized card for project showcase
interface ProjectCardProps extends AnimatedCardProps {
  image?: string
  title: string
  description: string
  tags?: string[]
}

export function ProjectCard({ 
  image, 
  title, 
  description, 
  tags, 
  className,
  ...props 
}: ProjectCardProps) {
  return (
    <AnimatedCard 
      variant="hover-lift" 
      className={cn("group", className)}
      {...props}
    >
      {image && (
        <div className="aspect-video overflow-hidden">
          <motion.img
            src={image}
            alt={title}
            className="w-full h-full object-cover"
            whileHover={{ scale: 1.1 }}
            transition={{ duration: 0.3 }}
          />
        </div>
      )}
      <div className="p-6">
        <h3 className="text-xl font-bold mb-2 group-hover:text-tappel-orange transition-colors">
          {title}
        </h3>
        <p className="text-gray-600 mb-4">
          {description}
        </p>
        {tags && (
          <div className="flex flex-wrap gap-2">
            {tags.map((tag, index) => (
              <span 
                key={index}
                className="px-2 py-1 bg-gray-100 text-gray-700 text-sm rounded-full"
              >
                {tag}
              </span>
            ))}
          </div>
        )}
      </div>
    </AnimatedCard>
  )
}

// Specialized card for services
interface ServiceCardProps extends Omit<AnimatedCardProps, 'children'> {
  icon?: React.ReactNode
  title: string
  description: string
  image?: string
  href?: string
}

export function ServiceCard({
  icon,
  title,
  description,
  image,
  href,
  className,
  ...props
}: ServiceCardProps) {
  const CardContent = (
    <AnimatedCard
      variant="hover-glow"
      className={cn("group text-center overflow-hidden", className)}
      {...props}
    >
      {image && (
        <div className="relative h-48 overflow-hidden">
          <Image
            src={image}
            alt={title}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
          />
        </div>
      )}
      <div className="p-6">
        {icon && (
          <motion.div
            className="w-12 h-12 bg-tappel-orange rounded-full flex items-center justify-center mb-4 mx-auto group-hover:bg-tappel-blue transition-colors"
            whileHover={{ rotate: 360 }}
            transition={{ duration: 0.5 }}
          >
            {icon}
          </motion.div>
        )}
        <h3 className="text-xl font-bold mb-3 group-hover:text-tappel-orange transition-colors">
          {title}
        </h3>
        <p className="text-gray-600 mb-4">
          {description}
        </p>
        {href && (
          <div className="flex items-center justify-center text-tappel-orange font-medium group-hover:text-tappel-blue transition-colors">
            Meer info <ArrowRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" />
          </div>
        )}
      </div>
    </AnimatedCard>
  )

  if (href) {
    return (
      <Link href={href} className="block">
        {CardContent}
      </Link>
    )
  }

  return CardContent
}

export { AnimatedCard }
