"use client"

import { motion, useScroll, useSpring } from "framer-motion"
import { useEffect, useState } from "react"

interface ScrollProgressProps {
  className?: string
  color?: string
  height?: number
  showPercentage?: boolean
}

export function ScrollProgress({ 
  className = "", 
  color = "#FF6B35", 
  height = 3,
  showPercentage = false 
}: ScrollProgressProps) {
  const { scrollYProgress } = useScroll()
  const scaleX = useSpring(scrollYProgress, {
    stiffness: 400,
    damping: 40,
    restDelta: 0.001
  })
  
  const [percentage, setPercentage] = useState(0)

  useEffect(() => {
    if (showPercentage) {
      return scrollYProgress.on("change", (latest) => {
        setPercentage(Math.round(latest * 100))
      })
    }
  }, [scrollYProgress, showPercentage])

  return (
    <>
      {/* Progress Bar */}
      <motion.div
        className={`fixed top-0 left-0 right-0 z-50 origin-left ${className}`}
        style={{
          scaleX,
          height: `${height}px`,
          backgroundColor: color,
          transformOrigin: "0%"
        }}
      />
      
      {/* Optional Percentage Display */}
      {showPercentage && (
        <motion.div
          className="fixed top-4 right-4 z-50 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-medium shadow-lg"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.5 }}
        >
          {percentage}%
        </motion.div>
      )}
    </>
  )
}
