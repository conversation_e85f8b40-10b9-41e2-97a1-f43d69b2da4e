interface LocalContactInfoProps {
  city: string;
  localPhone?: string;
  serviceArea?: string[];
  emergencyAvailable?: boolean;
}

export default function LocalContactInfo({ 
  city, 
  localPhone = "06-21452819", 
  serviceArea = [], 
  emergencyAvailable = true 
}: LocalContactInfoProps) {
  const getLocalServiceInfo = (city: string) => {
    switch (city) {
      case "Rotterdam":
        return {
          serviceRadius: "25km rondom Rotterdam",
          responseTime: "Binnen 2 uur in Rotterdam",
          specialties: ["Havengebied", "Industriële complexen", "Wederopbouw flats"],
          emergencyAreas: ["Centrum", "<PERSON><PERSON> van Z<PERSON>", "Overschie", "Kralingen", "Botlek", "Europoort"]
        };
      case "Den Haag":
        return {
          serviceRadius: "20km rondom Den Haag",
          responseTime: "Binnen 1.5 uur in Den Haag",
          specialties: ["Monumentale panden", "Overheidsgebouwen", "Welstandsvergunningen"],
          emergencyAreas: ["Centrum", "Voorhout", "Bezuidenhout", "Schilderswijk", "Leidschenveen"]
        };
      case "Zoetermeer":
        return {
          serviceRadius: "15km rondom Zoetermeer",
          responseTime: "Binnen 1 uur in Zoetermeer",
          specialties: ["Jaren 80-90 nieuwbouw", "Moderne woonwijken", "Bedrijventerreinen"],
          emergencyAreas: ["Rokkeveen", "Buytenwegh", "Meerzicht", "Oosterheem", "Noordhove"]
        };
      case "Amsterdam":
        return {
          serviceRadius: "25km rondom Amsterdam",
          responseTime: "Binnen 1.5 uur in Amsterdam",
          specialties: ["Grachtenpanden", "Groendaken", "Klimaatadaptatie"],
          emergencyAreas: ["Centrum", "Jordaan", "Noord", "Zuidoost", "Oud-Zuid", "Zuidas"]
        };
      case "Utrecht":
        return {
          serviceRadius: "20km rondom Utrecht",
          responseTime: "Binnen 1 uur in Utrecht",
          specialties: ["Binnenstad", "Universiteitsgebouwen", "Leidsche Rijn"],
          emergencyAreas: ["Centrum", "Leidsche Rijn", "Overvecht", "Kanaleneiland"]
        };
      case "Haarlem":
        return {
          serviceRadius: "18km rondom Haarlem",
          responseTime: "Binnen 1 uur in Haarlem",
          specialties: ["Bloemenstad", "Culturele gebouwen", "Historisch centrum"],
          emergencyAreas: ["Centrum", "Schalkwijk", "Noord", "Botermarkt"]
        };
      default:
        return {
          serviceRadius: "20km rondom " + city,
          responseTime: "Binnen 2 uur in " + city,
          specialties: ["Platte daken", "EPDM renovatie", "Bitumen renovatie"],
          emergencyAreas: [city + " centrum"]
        };
    }
  };

  const serviceInfo = getLocalServiceInfo(city);

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h3 className="text-xl font-bold mb-4 text-tappel-orange">
        Lokale Service {city}
      </h3>
      
      <div className="grid md:grid-cols-2 gap-4 mb-6">
        <div>
          <h4 className="font-semibold mb-2">📞 Direct Contact</h4>
          <p className="text-gray-600 mb-1">Telefoon: <strong>{localPhone}</strong></p>
          <p className="text-gray-600 mb-1">Email: <EMAIL></p>
          <p className="text-gray-600">Reactietijd: {serviceInfo.responseTime}</p>
        </div>
        
        <div>
          <h4 className="font-semibold mb-2">🗺️ Servicegebied</h4>
          <p className="text-gray-600 mb-1">{serviceInfo.serviceRadius}</p>
          <p className="text-gray-600">Inclusief omliggende gemeenten</p>
        </div>
      </div>

      <div className="mb-4">
        <h4 className="font-semibold mb-2">🏗️ Lokale Specialiteiten</h4>
        <div className="flex flex-wrap gap-2">
          {serviceInfo.specialties.map((specialty, index) => (
            <span 
              key={index}
              className="bg-tappel-orange/10 text-tappel-orange px-3 py-1 rounded-full text-sm"
            >
              {specialty}
            </span>
          ))}
        </div>
      </div>

      {emergencyAvailable && (
        <div className="bg-red-50 p-4 rounded-lg">
          <h4 className="font-semibold mb-2 text-red-700">🚨 24/7 Spoedservice</h4>
          <p className="text-red-600 text-sm mb-2">
            Bij acute daklekkages zijn wij 24/7 bereikbaar voor spoedservice in:
          </p>
          <div className="flex flex-wrap gap-1">
            {serviceInfo.emergencyAreas.map((area, index) => (
              <span 
                key={index}
                className="bg-red-100 text-red-700 px-2 py-1 rounded text-xs"
              >
                {area}
              </span>
            ))}
          </div>
          <p className="text-red-600 text-sm mt-2">
            <strong>Spoednummer: {localPhone}</strong>
          </p>
        </div>
      )}
    </div>
  );
}
