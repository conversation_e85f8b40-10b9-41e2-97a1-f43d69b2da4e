# Invisible reCAPTCHA Implementatie

## Overzicht
De website gebruikt nu **Invisible reCAPTCHA** volgens de Google documentatie. Dit biedt een veel betere gebruikerservaring omdat gebruikers geen checkbox hoeven aan te vinken.

## Hoe het werkt

### 1. Gebruikerservaring
- **Gee<PERSON> zichtbare reCAPTCHA widget** op het formulier
- **Automatische verificatie** wanneer gebruiker op "VERSTUREN" klikt
- **Naadloze integratie** - gebruiker merkt niets van de verificatie
- **Alleen bij verdachte activiteit** wordt een challenge getoond

### 2. Technische implementatie

#### Components
- **`useInvisibleRecaptcha()` hook**: Beheert de invisible reCAPTCHA widget
- **`InvisibleReCaptchaContainer`**: Verborgen container voor de widget
- **Automatische uitvoering**: Bij form submit wordt reCAPTCHA automatisch uitgevoerd

#### Flow
1. Gebruiker vult formulier in
2. <PERSON><PERSON><PERSON><PERSON><PERSON> klikt "VERSTUREN"
3. `executeRecaptcha()` wordt aangeroepen
4. Google analyseert gedrag in de achtergrond
5. Bij goedkeuring: token wordt gegenereerd en formulier verzonden
6. Bij verdachte activiteit: challenge wordt getoond

### 3. Configuratie

#### Environment Variables
```bash
# Site Key (publiek)
NEXT_PUBLIC_RECAPTCHA_SITE_KEY=6LeRiL0rAAAAAJ5xM7FK0ZG5HJ_aNI393vEhTJrD

# Secret Key (privé) - nog toe te voegen
RECAPTCHA_SECRET_KEY=6LeRiL0rAAAAAXXXXXXXXXXXXXXXXXXXXXXXXXXX
```

#### Google reCAPTCHA Console
- **Type**: reCAPTCHA v2 Invisible
- **Domains**: localhost, tappeldakwerken.nl, www.tappeldakwerken.nl

## Voordelen

### Voor Gebruikers
✅ **Geen extra stappen** - geen checkbox om aan te vinken
✅ **Snellere workflow** - direct versturen mogelijk
✅ **Minder frustratie** - geen "Selecteer alle verkeerslichten"
✅ **Mobiel-vriendelijk** - geen kleine checkboxes

### Voor Website
✅ **Hogere conversie** - minder abandonment
✅ **Betere UX** - naadloze integratie
✅ **Zelfde beveiliging** - Google's geavanceerde detectie
✅ **Minder support** - geen reCAPTCHA gerelateerde vragen

## Testing

### Normale Gebruiker
1. Ga naar contactformulier
2. Vul gegevens in
3. Klik "VERSTUREN"
4. Formulier wordt direct verzonden (geen zichtbare reCAPTCHA)

### Verdachte Activiteit Simuleren
- Gebruik VPN/Tor
- Snel herhaaldelijk versturen
- Geautomatiseerd gedrag
- Dan wordt challenge getoond

## Troubleshooting

### "reCAPTCHA not ready"
- Controleer of script geladen is
- Wacht tot `window.grecaptcha` beschikbaar is

### "Invalid site key"
- Controleer `NEXT_PUBLIC_RECAPTCHA_SITE_KEY`
- Verifieer domein in Google Console

### Challenge wordt altijd getoond
- Controleer site key type (moet invisible zijn)
- Controleer domein configuratie

## Monitoring

### Console Logs
- `reCAPTCHA loaded` - Script succesvol geladen
- `reCAPTCHA success: [token]` - Verificatie succesvol
- `reCAPTCHA expired` - Token verlopen
- `reCAPTCHA error occurred` - Fout opgetreden

### Productie Monitoring
- Monitor form submission rates
- Track reCAPTCHA success/failure rates
- Monitor voor spam/bot activiteit
