"use client"

import { motion, AnimatePresence } from "framer-motion"
import { useState, useRef, useEffect } from "react"
import { cn } from "@/lib/utils"

interface AnimatedBackgroundProps {
  children: React.ReactNode
  className?: string
  defaultValue?: string
  transition?: {
    type?: string
    bounce?: number
    duration?: number
  }
  enableHover?: boolean
}

export function AnimatedBackground({
  children,
  className,
  defaultValue,
  transition = { type: "spring", bounce: 0.2, duration: 0.4 },
  enableHover = true
}: AnimatedBackgroundProps) {
  const [activeId, setActiveId] = useState<string | null>(defaultValue || null)
  const [hoveredId, setHoveredId] = useState<string | null>(null)
  const [dimensions, setDimensions] = useState({ width: 0, height: 0, left: 0, top: 0 })
  const containerRef = useRef<HTMLDivElement>(null)

  const updateDimensions = (element: HTMLElement) => {
    if (!containerRef.current) return
    
    const containerRect = containerRef.current.getBoundingClientRect()
    const elementRect = element.getBoundingClientRect()
    
    setDimensions({
      width: elementRect.width,
      height: elementRect.height,
      left: elementRect.left - containerRect.left,
      top: elementRect.top - containerRect.top
    })
  }

  useEffect(() => {
    if (activeId) {
      const activeElement = containerRef.current?.querySelector(`[data-id="${activeId}"]`) as HTMLElement
      if (activeElement) {
        updateDimensions(activeElement)
      }
    }
  }, [activeId])

  const handleMouseEnter = (id: string, element: HTMLElement) => {
    if (enableHover) {
      setHoveredId(id)
      updateDimensions(element)
    }
  }

  const handleMouseLeave = () => {
    if (enableHover) {
      setHoveredId(null)
      if (activeId) {
        const activeElement = containerRef.current?.querySelector(`[data-id="${activeId}"]`) as HTMLElement
        if (activeElement) {
          updateDimensions(activeElement)
        }
      }
    }
  }

  const handleClick = (id: string, element: HTMLElement) => {
    setActiveId(id)
    updateDimensions(element)
  }

  const showBackground = hoveredId || activeId
  const backgroundId = hoveredId || activeId

  return (
    <div ref={containerRef} className={cn("relative", className)}>
      <AnimatePresence>
        {showBackground && (
          <motion.div
            className="absolute bg-tappel-orange/10 rounded-lg border border-tappel-orange/20"
            layoutId="background"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{
              opacity: 1,
              scale: 1,
              width: dimensions.width,
              height: dimensions.height,
              x: dimensions.left,
              y: dimensions.top
            }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={transition}
            style={{ zIndex: 0 }}
          />
        )}
      </AnimatePresence>
      
      <div className="relative z-10">
        {Array.isArray(children) 
          ? children.map((child, index) => {
              if (child && typeof child === 'object' && 'props' in child) {
                const dataId = child.props['data-id']
                return (
                  <div
                    key={dataId || index}
                    data-id={dataId}
                    onMouseEnter={(e) => dataId && handleMouseEnter(dataId, e.currentTarget)}
                    onMouseLeave={handleMouseLeave}
                    onClick={(e) => dataId && handleClick(dataId, e.currentTarget)}
                    className="relative"
                  >
                    {child}
                  </div>
                )
              }
              return child
            })
          : children
        }
      </div>
    </div>
  )
}

// Specialized component for navigation items
interface NavItemProps {
  children: React.ReactNode
  'data-id': string
  className?: string
  isActive?: boolean
}

export function NavItem({ children, 'data-id': dataId, className, isActive }: NavItemProps) {
  return (
    <div
      data-id={dataId}
      className={cn(
        "px-4 py-2 rounded-lg transition-colors cursor-pointer relative z-10",
        isActive ? "text-tappel-orange" : "text-gray-700 hover:text-tappel-orange",
        className
      )}
    >
      {children}
    </div>
  )
}

// Tab-style animated background
interface AnimatedTabsProps {
  tabs: Array<{
    id: string
    label: string
    content?: React.ReactNode
  }>
  defaultTab?: string
  onTabChange?: (tabId: string) => void
  className?: string
}

export function AnimatedTabs({
  tabs,
  defaultTab,
  onTabChange,
  className
}: AnimatedTabsProps) {
  const [activeTab, setActiveTab] = useState(defaultTab || tabs[0]?.id)

  const handleTabClick = (tabId: string) => {
    setActiveTab(tabId)
    onTabChange?.(tabId)
  }

  return (
    <div className={cn("w-full", className)}>
      <AnimatedBackground defaultValue={activeTab} enableHover={false}>
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              data-id={tab.id}
              onClick={() => handleTabClick(tab.id)}
              className={cn(
                "px-4 py-2 text-sm font-medium rounded-md transition-colors relative z-10",
                activeTab === tab.id
                  ? "text-tappel-orange"
                  : "text-gray-600 hover:text-gray-900"
              )}
            >
              {tab.label}
            </button>
          ))}
        </div>
      </AnimatedBackground>
      
      {/* Tab Content */}
      <div className="mt-4">
        {tabs.map((tab) => (
          <AnimatePresence key={tab.id} mode="wait">
            {activeTab === tab.id && tab.content && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.2 }}
              >
                {tab.content}
              </motion.div>
            )}
          </AnimatePresence>
        ))}
      </div>
    </div>
  )
}

// Floating action button with animated background
interface FloatingActionProps {
  children: React.ReactNode
  className?: string
  onClick?: () => void
  variant?: "primary" | "secondary"
}

export function FloatingAction({
  children,
  className,
  onClick,
  variant = "primary"
}: FloatingActionProps) {
  const [isHovered, setIsHovered] = useState(false)

  const variantClasses = {
    primary: "bg-tappel-orange text-white shadow-lg shadow-tappel-orange/25",
    secondary: "bg-white text-tappel-orange shadow-lg border border-gray-200"
  }

  return (
    <motion.button
      className={cn(
        "fixed bottom-6 right-6 p-4 rounded-full z-50 transition-all duration-300",
        variantClasses[variant],
        className
      )}
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.9 }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={onClick}
    >
      {children}
      
      {/* Animated background ring */}
      <motion.div
        className="absolute inset-0 rounded-full border-2 border-tappel-orange"
        initial={{ scale: 1, opacity: 0 }}
        animate={{
          scale: isHovered ? 1.2 : 1,
          opacity: isHovered ? 0.6 : 0
        }}
        transition={{ duration: 0.3 }}
      />
    </motion.button>
  )
}
