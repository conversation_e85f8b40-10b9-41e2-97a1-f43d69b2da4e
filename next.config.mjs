/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
  // Docker-friendly settings
  output: 'standalone',
  productionBrowserSourceMaps: false,
  outputFileTracingRoot: process.cwd(),

  // Webpack configuration to prevent Docker issues
  webpack: (config, { isServer }) => {
    // Prevent webpack from trying to resolve optional dependencies
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      net: false,
      tls: false,
    };

    return config;
  },

  async redirects() {
    return [
      {
        source: '/projecten/montageankers-boskoop',
        destination: '/projecten/dakrenovatie-montageankers-zoeterwoude',
        permanent: true,
      },
    ]
  },
}

export default nextConfig
