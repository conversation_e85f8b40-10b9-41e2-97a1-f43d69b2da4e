interface CommercialBenefitsProps {
  city: string;
}

export default function CommercialBenefits({ city }: CommercialBenefitsProps) {
  const getCityBenefits = (city: string) => {
    switch (city) {
      case "Rotterdam":
        return {
          valueIncrease: "15-25%",
          energySavings: "40-60%",
          localBenefits: [
            "Bestand tegen zeewind en industriële omgeving",
            "Verhoogt waarde van havenstad vastgoed",
            "Vermindert geluidshinder van haven en verkeer",
            "Geschikt voor Rotterdam's wisselende klimaat"
          ]
        };
      case "Amsterdam":
        return {
          valueIncrease: "20-30%",
          energySavings: "35-55%",
          localBenefits: [
            "Draagt bij aan Amsterdam's klimaatdoelstellingen",
            "Verhoogt waarde van grachtenpand aanzienlijk",
            "Groendaken verbeteren luchtkwaliteit in de stad",
            "Past bij Amsterdam's duurzaamheidsbeleid"
          ]
        };
      case "Den Haag":
        return {
          valueIncrease: "18-28%",
          energySavings: "35-50%",
          localBenefits: [
            "Voldoet aan strenge welstandseisen",
            "Verhoogt waarde van monumentale panden",
            "Geschikt voor overheidsgebouwen",
            "Respecteert historische architectuur"
          ]
        };
      case "Utrecht":
        return {
          valueIncrease: "16-26%",
          energySavings: "40-55%",
          localBenefits: [
            "Geschikt voor universitaire omgeving",
            "Verhoogt waarde van centraal gelegen vastgoed",
            "Ondersteunt Utrecht's groene ambities",
            "Ideaal voor moderne nieuwbouw Leidsche Rijn"
          ]
        };
      default:
        return {
          valueIncrease: "15-25%",
          energySavings: "35-50%",
          localBenefits: [
            "Verhoogt de waarde van uw woning aanzienlijk",
            "Bespaart energiekosten door betere isolatie",
            "Voorkomt kostbare waterschade",
            "Draagt bij aan duurzame leefomgeving"
          ]
        };
    }
  };

  const benefits = getCityBenefits(city);

  return (
    <div className="bg-gradient-to-r from-tappel-blue to-blue-600 text-white p-8 rounded-lg">
      <h3 className="text-2xl font-bold mb-6 text-center">
        Waarom Investeren in Dakrenovatie {city}?
      </h3>
      
      <div className="grid md:grid-cols-3 gap-6 mb-8">
        <div className="text-center">
          <div className="text-3xl font-bold text-tappel-orange mb-2">
            {benefits.valueIncrease}
          </div>
          <div className="text-lg font-semibold mb-2">Waardeverhoging</div>
          <p className="text-sm text-white/80">
            Gemiddelde waardestijging van uw pand na professionele dakrenovatie
          </p>
        </div>
        
        <div className="text-center">
          <div className="text-3xl font-bold text-tappel-orange mb-2">
            {benefits.energySavings}
          </div>
          <div className="text-lg font-semibold mb-2">Energiebesparing</div>
          <p className="text-sm text-white/80">
            Reductie in energiekosten door verbeterde dakisolatie
          </p>
        </div>
        
        <div className="text-center">
          <div className="text-3xl font-bold text-tappel-orange mb-2">
            10+
          </div>
          <div className="text-lg font-semibold mb-2">Jaar Garantie</div>
          <p className="text-sm text-white/80">
            Volledige garantie op materiaal en vakmanschap
          </p>
        </div>
      </div>

      <div className="border-t border-white/20 pt-6">
        <h4 className="text-lg font-semibold mb-4">Lokale Voordelen {city}:</h4>
        <div className="grid md:grid-cols-2 gap-3">
          {benefits.localBenefits.map((benefit, index) => (
            <div key={index} className="flex items-start gap-3">
              <div className="w-2 h-2 bg-tappel-orange rounded-full mt-2 flex-shrink-0"></div>
              <span className="text-sm">{benefit}</span>
            </div>
          ))}
        </div>
      </div>

      <div className="text-center mt-8">
        <div className="bg-white/10 p-4 rounded-lg inline-block">
          <p className="text-lg font-semibold mb-2">
            💰 Gemiddelde ROI: 3-5 jaar
          </p>
          <p className="text-sm text-white/80">
            Uw investering verdient zichzelf terug door energiebesparing en waardeverhoging
          </p>
        </div>
      </div>
    </div>
  );
}
