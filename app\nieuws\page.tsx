import Image from "next/image"
import Link from "next/link"
import { Calendar, User, ArrowLeft, ArrowRight } from "lucide-react"
import { AnimatedText } from "@/components/motion/animated-text"
import { AnimatedButton } from "@/components/motion/animated-button"
import { AnimatedCard } from "@/components/motion/animated-card"
import type { Metadata } from "next"

export const metadata: Metadata = {
  title: "Nieuws | Tappel Dakwerken - Laatste Updates & Projecten",
  description: "Blijf op de hoogte van de laatste nieuwtjes, projectupdates en ontwikkelingen bij Tappel Dakwerken. Ontdek onze nieuwste dakrenovatie projecten en innovaties.",
  keywords: "Tappel Dakwerken nieuws, dakwerken updates, projectnieuws, dakrenovatie nieuws, dakdoorvoer nieuws",
  alternates: {
    canonical: "/nieuws",
  },
  openGraph: {
    title: "Nieuws | Tappel Dakwerken - Laatste Updates & Projecten",
    description: "Blijf op de hoogte van de laatste nieuwtjes, projectupdates en ontwikkelingen bij Tappel Dakwerken.",
    url: "https://tappeldakwerken.nl/nieuws",
    siteName: "Tappel Dakwerken",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Tappel Dakwerken Nieuws",
      },
    ],
    locale: "nl_NL",
    type: "website",
  },
  category: "Nieuws",
}

// Sample news articles
const newsArticles = [
  {
    id: "nieuwe-dakdoorvoer-technologie-2024",
    title: "Revolutionaire Dakdoorvoer Technologie Geïntroduceerd",
    excerpt: "Tappel Dakwerken introduceert geavanceerde dakdoorvoer technologie die 40% sneller te installeren is en nog beter beschermt tegen lekkages.",
    content: `
      <p>We zijn trots om onze nieuwste innovatie aan te kondigen: een revolutionaire dakdoorvoer technologie die de installatie van zonnepanelen op platte daken naar een hoger niveau tilt.</p>
      
      <h3>Wat maakt deze technologie zo bijzonder?</h3>
      <ul>
        <li><strong>40% snellere installatie:</strong> Door ons nieuwe kliksysteem kunnen dakdoorvoeren nu in recordtijd worden geplaatst</li>
        <li><strong>Verbeterde waterdichtheid:</strong> Een innovatieve afdichtingsring zorgt voor 99.9% garantie tegen lekkages</li>
        <li><strong>Duurzamer materiaal:</strong> Gemaakt van gerecycled aluminium met 25 jaar garantie</li>
        <li><strong>Universele compatibiliteit:</strong> Geschikt voor alle gangbare dakbedekkingen</li>
      </ul>
      
      <p>Deze doorbraak is het resultaat van 2 jaar intensief onderzoek en ontwikkeling in samenwerking met Nederlandse technische universiteiten.</p>
      
      <h3>Voordelen voor onze klanten</h3>
      <p>Door de snellere installatie kunnen we onze klanten nu nog scherper prijzen aanbieden, terwijl de kwaliteit en betrouwbaarheid alleen maar beter worden. De nieuwe technologie is vanaf maart 2024 beschikbaar voor alle nieuwe projecten.</p>
    `,
    image: "/images/nieuws/nieuwe-dakdoorvoer-technologie.jpg",
    author: "Jan Tappel",
    date: "2024-02-15",
    category: "Innovatie",
    featured: true
  },
  {
    id: "grootschalig-project-amsterdam-noord",
    title: "Grootschalig Dakrenovatie Project Amsterdam Noord Afgerond",
    excerpt: "Succesvol 50 woningen voorzien van nieuwe EPDM dakbedekking en 200+ dakdoorvoeren voor zonnepanelen in Amsterdam Noord.",
    content: `
      <p>Met trots kunnen we melden dat ons grootste project van 2024 succesvol is afgerond. In Amsterdam Noord hebben we 50 woningen volledig gerenoveerd met nieuwe EPDM dakbedekking.</p>
      
      <h3>Projectdetails</h3>
      <ul>
        <li>50 woningen volledig gerenoveerd</li>
        <li>2.500 m² nieuwe EPDM dakbedekking</li>
        <li>200+ dakdoorvoeren geplaatst</li>
        <li>Projectduur: 6 maanden</li>
        <li>100% tevreden bewoners</li>
      </ul>
      
      <p>Dit project toont onze expertise in grootschalige renovaties aan. Door slimme planning en ons ervaren team konden we alle werkzaamheden uitvoeren zonder overlast voor bewoners.</p>
    `,
    image: "/images/nieuws/amsterdam-noord-project.jpg",
    author: "Petra de Vries",
    date: "2024-01-28",
    category: "Projecten",
    featured: false
  },
  {
    id: "duurzaamheidscertificaat-behaald",
    title: "Tappel Dakwerken Behaalt Duurzaamheidscertificaat",
    excerpt: "Officiële erkenning voor onze inzet op het gebied van duurzaam dakwerk en milieuvriendelijke werkwijzen.",
    content: `
      <p>We zijn verheugd te kunnen melden dat Tappel Dakwerken het prestigieuze Duurzaamheidscertificaat heeft behaald van de Nederlandse Dakdekkers Vereniging.</p>
      
      <h3>Onze duurzame initiatieven</h3>
      <ul>
        <li>100% recycling van oude dakmaterialen</li>
        <li>Gebruik van milieuvriendelijke materialen</li>
        <li>CO2-neutrale bedrijfsvoering</li>
        <li>Elektrische bedrijfswagens</li>
        <li>Zonnepanelen op ons eigen bedrijfspand</li>
      </ul>
      
      <p>Dit certificaat bevestigt onze toewijding aan een duurzame toekomst en ons streven om de ecologische voetafdruk van dakwerkzaamheden te minimaliseren.</p>
    `,
    image: "/images/nieuws/duurzaamheidscertificaat.jpg",
    author: "Jan Tappel",
    date: "2024-01-10",
    category: "Certificering",
    featured: false
  }
]

export default function NieuwsPage() {
  const featuredArticle = newsArticles.find(article => article.featured)
  const otherArticles = newsArticles.filter(article => !article.featured)

  return (
    <>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-gray-50 to-white py-12 md:py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <AnimatedText variant="fade-up">
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                <span className="text-tappel-orange">Nieuws</span> & Updates
              </h1>
            </AnimatedText>
            <AnimatedText variant="fade-up" delay={0.2}>
              <p className="text-xl text-gray-700">
                Blijf op de hoogte van onze laatste projecten, innovaties en ontwikkelingen in de dakwerken sector.
              </p>
            </AnimatedText>
          </div>
        </div>
      </section>

      {/* Featured Article */}
      {featuredArticle && (
        <section className="py-16 md:py-24">
          <div className="container mx-auto px-4">
            <AnimatedText variant="fade-up">
              <h2 className="text-3xl font-bold mb-12 text-center">
                Uitgelicht <span className="text-tappel-orange">Nieuws</span>
              </h2>
            </AnimatedText>
            
            <div className="max-w-4xl mx-auto">
              <AnimatedText variant="fade-up" delay={0.2}>
                
                  <AnimatedCard variant="hover-lift" className="bg-white rounded-2xl overflow-hidden shadow-lg border border-gray-100">
                    <div className="md:flex">
                      <div className="md:w-1/2">
                        <div className="relative h-64 md:h-full">
                          <Image
                            src={featuredArticle.image}
                            alt={featuredArticle.title}
                            fill
                            className="object-cover"
                          />
                          <div className="absolute top-4 left-4 bg-tappel-orange text-white px-3 py-1 rounded-full text-sm font-medium">
                            {featuredArticle.category}
                          </div>
                        </div>
                      </div>
                      <div className="md:w-1/2 p-8">
                        <div className="flex items-center gap-4 text-sm text-gray-600 mb-4">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            {new Date(featuredArticle.date).toLocaleDateString('nl-NL', {
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric'
                            })}
                          </div>
                          <div className="flex items-center gap-1">
                            <User className="h-4 w-4" />
                            {featuredArticle.author}
                          </div>
                        </div>
                        <h3 className="text-2xl font-bold mb-4">{featuredArticle.title}</h3>
                        <p className="text-gray-600 mb-6">{featuredArticle.excerpt}</p>
                        <AnimatedButton asChild variant="outline" className="group">
                          <Link href={`/nieuws/${featuredArticle.id}`}>
                            Lees meer <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                          </Link>
                        </AnimatedButton>
                      </div>
                    </div>
                  </AnimatedCard>
                
              </AnimatedText>
            </div>
          </div>
        </section>
      )}

      {/* Other Articles */}
      <section className="py-16 md:py-24 bg-gradient-to-br from-gray-50 to-white">
        <div className="container mx-auto px-4">
          <AnimatedText variant="fade-up">
            <h2 className="text-3xl font-bold mb-12 text-center">
              Meer <span className="text-tappel-orange">Nieuws</span>
            </h2>
          </AnimatedText>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {otherArticles.map((article, index) => (
              <AnimatedText key={article.id} variant="fade-up" delay={0.1 + index * 0.1}>
                
                  <AnimatedCard variant="hover-lift" className="bg-white rounded-2xl overflow-hidden shadow-lg border border-gray-100 flex flex-col h-full">
                    <div className="relative h-48 flex-shrink-0">
                      <Image
                        src={article.image}
                        alt={article.title}
                        fill
                        className="object-cover"
                      />
                      <div className="absolute top-4 left-4 bg-tappel-orange text-white px-3 py-1 rounded-full text-sm font-medium">
                        {article.category}
                      </div>
                    </div>
                    <div className="p-6 flex flex-col flex-grow">
                      <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          {new Date(article.date).toLocaleDateString('nl-NL', {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric'
                          })}
                        </div>
                        <div className="flex items-center gap-1">
                          <User className="h-4 w-4" />
                          {article.author}
                        </div>
                      </div>
                      <h3 className="text-xl font-bold mb-3 line-clamp-2">{article.title}</h3>
                      <p className="text-gray-600 mb-6 flex-grow line-clamp-3">{article.excerpt}</p>
                      <AnimatedButton asChild variant="outline" className="group w-full mt-auto">
                        <Link href={`/nieuws/${article.id}`}>
                          Lees meer <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                        </Link>
                      </AnimatedButton>
                    </div>
                  </AnimatedCard>
                
              </AnimatedText>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 md:py-24 bg-gradient-to-r from-tappel-orange to-orange-400 text-white">
        <div className="container mx-auto px-4 text-center">
          <AnimatedText variant="fade-up">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">Blijf op de hoogte</h2>
          </AnimatedText>
          <AnimatedText variant="fade-up" delay={0.2}>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Wilt u automatisch op de hoogte blijven van ons laatste nieuws en projectupdates? 
              Neem contact met ons op voor meer informatie.
            </p>
          </AnimatedText>
          <AnimatedText variant="fade-up" delay={0.4}>
            <AnimatedButton
              asChild
              size="lg"
              variant="secondary"
              className="bg-white text-tappel-orange hover:bg-gray-100 font-bold"
              glow
            >
              <Link href="/contact">NEEM CONTACT OP</Link>
            </AnimatedButton>
          </AnimatedText>
        </div>
      </section>
    </>
  )
}
