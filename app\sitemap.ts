import type { MetadataRoute } from "next"

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = 'https://tappeldakwerken.nl'

  // Static pages
  const staticPages = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 1,
    },
    {
      url: `${baseUrl}/diensten`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.9,
    },
    {
      url: `${baseUrl}/projecten`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/blog`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.7,
    },
    {
      url: `${baseUrl}/contact`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.8,
    },
  ]

  // Service pages - Zuid-Holland
  const zuidHollandPages = [
    'rotterdam', 'den-haag', 'zoetermeer', 'leiden',
    'delft', 'dordrecht', 'westland', 'gouda'
  ].map(city => ({
    url: `${baseUrl}/dakrenovatie-${city}`,
    lastModified: new Date(),
    changeFrequency: 'monthly' as const,
    priority: 0.8,
  }))

  // Service pages - Noord-Holland
  const noordHollandPages = [
    'amsterdam', 'haarlem', 'amstelveen', 'haarlemmermeer',
    'zaanstad', 'alkmaar', 'hilversum'
  ].map(city => ({
    url: `${baseUrl}/dakrenovatie-${city}`,
    lastModified: new Date(),
    changeFrequency: 'monthly' as const,
    priority: 0.8,
  }))

  // Service pages - Utrecht
  const utrechtPages = [
    'utrecht', 'amersfoort', 'nieuwegein', 'zeist', 'veenendaal'
  ].map(city => ({
    url: `${baseUrl}/dakrenovatie-${city}`,
    lastModified: new Date(),
    changeFrequency: 'monthly' as const,
    priority: 0.8,
  }))

  // Regional overview pages
  const regionalPages = [
    {
      url: `${baseUrl}/dakrenovatie-zuid-holland`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.9,
    },
    {
      url: `${baseUrl}/dakrenovatie-noord-holland`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.9,
    },
    {
      url: `${baseUrl}/dakrenovatie-provincie-utrecht`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.9,
    },
  ]

  // Project pages
  const projectPages = [
    'amsterdam-dakrenovatie-pv-demontage',
    'zoetermeer-dakrenovatie-rokkeveen',
    'zoeterwoude-dakrenovatie-bommelzolder',
    'den-haag-dakdoorvoer-zonnepanelen'
  ].map(project => ({
    url: `${baseUrl}/projecten/${project}`,
    lastModified: new Date(),
    changeFrequency: 'yearly' as const,
    priority: 0.6,
  }))

  return [
    ...staticPages,
    ...regionalPages,
    ...zuidHollandPages,
    ...noordHollandPages,
    ...utrechtPages,
    ...projectPages,
  ]
}

