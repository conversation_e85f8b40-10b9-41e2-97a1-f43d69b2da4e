"use client"

import { motion, useMotionValue, useSpring, useTransform } from "framer-motion"
import { useState, useRef, useEffect } from "react"
import { cn } from "@/lib/utils"
import Image from "next/image"
import { ChevronLeft, ChevronRight } from "lucide-react"

interface CardStackItem {
  id: string
  title: string
  description: string
  image?: string
  tags?: string[]
  location?: string
  year?: string
  href?: string // Add href for clickable cards
}

interface CardStackProps {
  items: CardStackItem[]
  className?: string
  maxVisible?: number
  stackOffset?: number
  scaleStep?: number
  rotationRange?: number
}

export function CardStack({
  items,
  className,
  maxVisible = 3,
  stackOffset = 10,
  scaleStep = 0.05,
  rotationRange = 5
}: CardStackProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })

  const nextCard = () => {
    setCurrentIndex((prev) => (prev + 1) % items.length)
  }

  const prevCard = () => {
    setCurrentIndex((prev) => (prev - 1 + items.length) % items.length)
  }

  const goToCard = (index: number) => {
    setCurrentIndex(index)
  }

  // Touch/swipe handlers
  const handleTouchStart = (e: React.TouchEvent) => {
    setDragStart({
      x: e.touches[0].clientX,
      y: e.touches[0].clientY
    })
    setIsDragging(true)
  }

  const handleTouchEnd = (e: React.TouchEvent) => {
    if (!isDragging) return

    const deltaX = e.changedTouches[0].clientX - dragStart.x
    const deltaY = Math.abs(e.changedTouches[0].clientY - dragStart.y)

    // Only trigger swipe if horizontal movement is greater than vertical
    if (Math.abs(deltaX) > 50 && Math.abs(deltaX) > deltaY) {
      if (deltaX > 0) {
        prevCard()
      } else {
        nextCard()
      }
    }

    setIsDragging(false)
  }

  // Mouse drag handlers for desktop
  const handleMouseDown = (e: React.MouseEvent) => {
    setDragStart({
      x: e.clientX,
      y: e.clientY
    })
    setIsDragging(true)
  }

  const handleMouseUp = (e: React.MouseEvent) => {
    if (!isDragging) return

    const deltaX = e.clientX - dragStart.x
    const deltaY = Math.abs(e.clientY - dragStart.y)

    if (Math.abs(deltaX) > 50 && Math.abs(deltaX) > deltaY) {
      if (deltaX > 0) {
        prevCard()
      } else {
        nextCard()
      }
    }

    setIsDragging(false)
  }

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!containerRef.current?.contains(document.activeElement)) return

      switch (e.key) {
        case 'ArrowLeft':
          e.preventDefault()
          prevCard()
          break
        case 'ArrowRight':
          e.preventDefault()
          nextCard()
          break
        case 'Home':
          e.preventDefault()
          goToCard(0)
          break
        case 'End':
          e.preventDefault()
          goToCard(items.length - 1)
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [items.length])

  // Get visible items starting from currentIndex
  const visibleItems = []
  for (let i = 0; i < Math.min(maxVisible, items.length); i++) {
    const itemIndex = (currentIndex + i) % items.length
    visibleItems.push({ ...items[itemIndex], originalIndex: itemIndex })
  }

  return (
    <div
      ref={containerRef}
      className={cn("relative w-full max-w-xl mx-auto focus-within:outline-none touch-pan-y group", className)}
      tabIndex={0}
      role="region"
      aria-label={`Card stack with ${items.length} items. Use arrow keys to navigate or swipe.`}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
    >
      <div className="relative h-[32rem]">
        {visibleItems.map((item, stackIndex) => {
          const isHovered = hoveredIndex === stackIndex
          const isTopCard = stackIndex === 0

          return (
            <StackCard
              key={`${item.id}-${currentIndex}`}
              item={item}
              index={stackIndex}
              isHovered={isHovered}
              isTopCard={isTopCard}
              stackOffset={stackOffset}
              scaleStep={scaleStep}
              rotationRange={rotationRange}
              onHover={() => setHoveredIndex(stackIndex)}
              onLeave={() => setHoveredIndex(null)}
              isDragging={isDragging}
            />
          )
        })}

        {/* Navigation Arrows */}
        {items.length > 1 && (
          <>
            <button
              onClick={prevCard}
              className="absolute left-4 top-1/2 -translate-y-1/2 z-[110] w-10 h-10 bg-white/95 hover:bg-white rounded-full shadow-xl border border-gray-200 flex items-center justify-center transition-all duration-300 hover:scale-110 backdrop-blur-sm opacity-60 group-hover:opacity-100 hover:opacity-100"
              aria-label="Previous card"
            >
              <ChevronLeft className="h-5 w-5 text-gray-700" />
            </button>

            <button
              onClick={nextCard}
              className="absolute right-4 top-1/2 -translate-y-1/2 z-[110] w-10 h-10 bg-white/95 hover:bg-white rounded-full shadow-xl border border-gray-200 flex items-center justify-center transition-all duration-300 hover:scale-110 backdrop-blur-sm opacity-60 group-hover:opacity-100 hover:opacity-100"
              aria-label="Next card"
            >
              <ChevronRight className="h-5 w-5 text-gray-700" />
            </button>
          </>
        )}
      </div>

      {/* Improved Indicator Dots */}
      {items.length > 1 && (
        <div className="flex justify-center mt-6 space-x-2">
          {items.map((_, index) => (
            <button
              key={index}
              onClick={() => goToCard(index)}
              className={cn(
                "w-3 h-3 rounded-full transition-all duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-tappel-orange focus:ring-offset-2",
                index === currentIndex
                  ? "bg-tappel-orange shadow-lg"
                  : "bg-gray-300 hover:bg-gray-400"
              )}
              aria-label={`Go to card ${index + 1}`}
            />
          ))}
        </div>
      )}

      {/* Swipe instruction for mobile */}
      <div className="text-center mt-4 text-sm text-gray-500 md:hidden">
        Swipe om te navigeren
      </div>
    </div>
  )
}

interface StackCardProps {
  item: CardStackItem
  index: number
  isHovered: boolean
  isTopCard: boolean
  stackOffset: number
  scaleStep: number
  rotationRange: number
  onHover: () => void
  onLeave: () => void
  isDragging: boolean
}

function StackCard({
  item,
  index,
  isHovered,
  isTopCard,
  stackOffset,
  scaleStep,
  rotationRange,
  onHover,
  onLeave,
  isDragging
}: StackCardProps) {
  const cardRef = useRef<HTMLDivElement>(null)
  const mouseX = useMotionValue(0)
  const mouseY = useMotionValue(0)

  // Reduced spring stiffness for better performance
  const springConfig = { stiffness: 200, damping: 25 }
  const rotateX = useSpring(useTransform(mouseY, [-0.5, 0.5], [rotationRange * 0.5, -rotationRange * 0.5]), springConfig)
  const rotateY = useSpring(useTransform(mouseX, [-0.5, 0.5], [-rotationRange * 0.5, rotationRange * 0.5]), springConfig)

  // Throttled mouse move for better performance
  const handleMouseMove = useRef<(e: React.MouseEvent) => void>()

  if (!handleMouseMove.current) {
    let rafId: number
    handleMouseMove.current = (e: React.MouseEvent) => {
      if (rafId) cancelAnimationFrame(rafId)
      rafId = requestAnimationFrame(() => {
        if (!cardRef.current) return
        const rect = cardRef.current.getBoundingClientRect()
        const centerX = rect.left + rect.width / 2
        const centerY = rect.top + rect.height / 2

        mouseX.set((e.clientX - centerX) / rect.width)
        mouseY.set((e.clientY - centerY) / rect.height)
      })
    }
  }

  const handleMouseLeave = () => {
    mouseX.set(0)
    mouseY.set(0)
    onLeave()
  }

  const handleCardClick = (e: React.MouseEvent) => {
    // Only navigate if we have an href and it's not a drag operation
    if (item.href && !isDragging) {
      window.location.href = item.href
    }
  }
  
  const baseY = index * stackOffset
  const baseScale = 1 - index * scaleStep
  const baseRotation = (index % 2 === 0 ? 1 : -1) * (index * 2)
  
  return (
    <motion.div
      ref={cardRef}
      className={cn(
        "absolute inset-0",
        item.href ? "cursor-pointer" : "cursor-default"
      )}
      style={{
        zIndex: isHovered ? 50 : 10 - index, // Reduced hover z-index to stay below arrows
        rotateX: isTopCard ? rotateX : 0,
        rotateY: isTopCard ? rotateY : 0,
        willChange: 'transform' // Optimize for GPU
      }}
      initial={{
        y: baseY,
        scale: baseScale,
        rotate: baseRotation,
        opacity: 0
      }}
      animate={{
        y: isHovered ? -15 : baseY, // Reduced movement
        scale: isHovered ? 1.03 : baseScale, // Reduced scale
        rotate: isHovered ? 0 : baseRotation,
        opacity: 1
      }}
      exit={{
        opacity: 0,
        scale: 0.9, // Less dramatic exit
        transition: { duration: 0.15 }
      }}
      transition={{
        type: "spring",
        stiffness: 250, // Reduced stiffness
        damping: 30
      }}
      onMouseMove={handleMouseMove.current}
      onMouseEnter={onHover}
      onMouseLeave={handleMouseLeave}
      onClick={handleCardClick}
    >
      <div className="w-full h-full bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
        {/* Image */}
        {item.image && (
          <div className="h-64 overflow-hidden">
            <motion.div
              className="w-full h-full"
              whileHover={{ scale: 1.1 }}
              transition={{ duration: 0.3 }}
            >
              <Image
                src={item.image}
                alt={item.title}
                width={500}
                height={280}
                className="w-full h-full object-cover"
              />
            </motion.div>
          </div>
        )}
        
        {/* Content */}
        <div className="p-6">
          <div className="flex justify-between items-start mb-2">
            <h3 className="text-xl font-bold text-gray-900 group-hover:text-tappel-orange transition-colors">
              {item.title}
            </h3>
            {item.year && (
              <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
                {item.year}
              </span>
            )}
          </div>
          
          {item.location && (
            <p className="text-sm text-tappel-orange font-medium mb-2">
              📍 {item.location}
            </p>
          )}
          
          <p className="text-gray-600 text-sm mb-4 line-clamp-3">
            {item.description}
          </p>
          
          {/* Tags */}
          {item.tags && (
            <div className="flex flex-wrap gap-2">
              {item.tags.slice(0, 3).map((tag, tagIndex) => (
                <span
                  key={tagIndex}
                  className="px-2 py-1 bg-tappel-orange/10 text-tappel-orange text-xs rounded-full"
                >
                  {tag}
                </span>
              ))}
            </div>
          )}
        </div>
        
        {/* Hover Glow Effect */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-tappel-orange/5 to-orange-400/5 rounded-xl"
          initial={{ opacity: 0 }}
          animate={{ opacity: isHovered ? 1 : 0 }}
          transition={{ duration: 0.3 }}
        />
      </div>
    </motion.div>
  )
}
