import Link from "next/link"
import Image from "next/image"
import { Phone, Mail, MapPin, Facebook, Instagram, Linkedin } from "lucide-react"

export default function Footer() {
  return (
    <footer className="bg-gray-100 border-t">
      <div className="container mx-auto px-4 py-12 md:py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-8">
          <div>
            <Link href="/" className="inline-block mb-4">
              <Image
                src="/images/tappel-dakwerken-logo.png"
                alt="Tappel Dakwerken logo – Specialist in dakdoorvoeren, dakrenovatie en montageankers voor platte daken"
                title="Tappel Dakwerken – Betrouwbare partner in dakdoorvoeren en dakrenovaties"
                width={100}
                height={100}
                className="object-contain"
              />
            </Link>
            <p className="text-gray-600 mb-4">
              Specialist in verduurzaming en renovatie van platte en licht hellende daken.
            </p>
            <div className="flex space-x-4">
              <Link href="#" className="text-gray-500 hover:text-tappel-orange transition-colors">
                <Facebook size={20} />
                <span className="sr-only">Facebook</span>
              </Link>
              <Link href="#" className="text-gray-500 hover:text-tappel-orange transition-colors">
                <Instagram size={20} />
                <span className="sr-only">Instagram</span>
              </Link>
              <Link href="#" className="text-gray-500 hover:text-tappel-orange transition-colors">
                <Linkedin size={20} />
                <span className="sr-only">LinkedIn</span>
              </Link>
            </div>
          </div>

          <div>
            <h3 className="font-bold text-lg mb-4">Diensten</h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/diensten"
                  className="text-gray-600 hover:text-tappel-orange transition-colors"
                >
                  Dakdoorvoeren
                </Link>
              </li>
              <li>
                <Link
                  href="/diensten"
                  className="text-gray-600 hover:text-tappel-orange transition-colors"
                >
                  Montageankers zonnepanelen
                </Link>
              </li>
              <li>
                <Link
                  href="/diensten"
                  className="text-gray-600 hover:text-tappel-orange transition-colors"
                >
                  Dakinspecties
                </Link>
              </li>
              <li>
                <Link
                  href="/diensten"
                  className="text-gray-600 hover:text-tappel-orange transition-colors"
                >
                  Dakrenovaties
                </Link>
              </li>
              <li>
                <Link
                  href="/diensten"
                  className="text-gray-600 hover:text-tappel-orange transition-colors"
                >
                  Lekkages opsporen
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="font-bold text-lg mb-4">Snelle Links</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/" className="text-gray-600 hover:text-tappel-orange transition-colors">
                  Home – Welkom bij Tappel Dakwerken
                </Link>
              </li>
              <li>
                <Link href="/projecten/" className="text-gray-600 hover:text-tappel-orange transition-colors">
                  Projecten
                </Link>
              </li>
              <li>
                <Link href="/over-ons/" className="text-gray-600 hover:text-tappel-orange transition-colors">
                  Over ons
                </Link>
              </li>
              <li>
                <Link href="/contact/" className="text-gray-600 hover:text-tappel-orange transition-colors">
                  Contact
                </Link>
              </li>
              <li>
                <Link href="/privacy-policy/" target="_blank" rel="noopener noreferrer" className="text-gray-600 hover:text-tappel-orange transition-colors">
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link href="/algemene-voorwaarden/" target="_blank" rel="noopener noreferrer" className="text-gray-600 hover:text-tappel-orange transition-colors">
                  Algemene Voorwaarden
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="font-bold text-lg mb-4">Werkgebied</h3>
            <ul className="space-y-2">
              <li>
                <h4 className="font-semibold text-gray-700 mb-2">Zuid-Holland</h4>
                <ul className="space-y-1 ml-2">
                  <li>
                    <Link href="/dakrenovatie-rotterdam" className="text-gray-600 hover:text-tappel-orange transition-colors text-sm">
                      Dakrenovatie Rotterdam
                    </Link>
                  </li>
                  <li>
                    <Link href="/dakrenovatie-den-haag" className="text-gray-600 hover:text-tappel-orange transition-colors text-sm">
                      Dakrenovatie Den Haag
                    </Link>
                  </li>
                  <li>
                    <Link href="/dakrenovatie-zoetermeer" className="text-gray-600 hover:text-tappel-orange transition-colors text-sm">
                      Dakrenovatie Zoetermeer
                    </Link>
                  </li>
                  <li>
                    <Link href="/dakrenovatie-leiden" className="text-gray-600 hover:text-tappel-orange transition-colors text-sm">
                      Dakrenovatie Leiden
                    </Link>
                  </li>
                  <li>
                    <Link href="/dakrenovatie-delft" className="text-gray-600 hover:text-tappel-orange transition-colors text-sm">
                      Dakrenovatie Delft
                    </Link>
                  </li>
                </ul>
              </li>
              <li className="mt-4">
                <h4 className="font-semibold text-gray-700 mb-2">Noord-Holland</h4>
                <ul className="space-y-1 ml-2">
                  <li>
                    <Link href="/dakrenovatie-amsterdam" className="text-gray-600 hover:text-tappel-orange transition-colors text-sm">
                      Dakrenovatie Amsterdam
                    </Link>
                  </li>
                  <li>
                    <Link href="/dakrenovatie-haarlem" className="text-gray-600 hover:text-tappel-orange transition-colors text-sm">
                      Dakrenovatie Haarlem
                    </Link>
                  </li>
                  <li>
                    <Link href="/dakrenovatie-amstelveen" className="text-gray-600 hover:text-tappel-orange transition-colors text-sm">
                      Dakrenovatie Amstelveen
                    </Link>
                  </li>
                  <li>
                    <Link href="/dakrenovatie-alkmaar" className="text-gray-600 hover:text-tappel-orange transition-colors text-sm">
                      Dakrenovatie Alkmaar
                    </Link>
                  </li>
                </ul>
              </li>
              <li className="mt-4">
                <h4 className="font-semibold text-gray-700 mb-2">Utrecht</h4>
                <ul className="space-y-1 ml-2">
                  <li>
                    <Link href="/dakrenovatie-utrecht" className="text-gray-600 hover:text-tappel-orange transition-colors text-sm">
                      Dakrenovatie Utrecht
                    </Link>
                  </li>
                  <li>
                    <Link href="/dakrenovatie-amersfoort" className="text-gray-600 hover:text-tappel-orange transition-colors text-sm">
                      Dakrenovatie Amersfoort
                    </Link>
                  </li>
                  <li>
                    <Link href="/dakrenovatie-nieuwegein" className="text-gray-600 hover:text-tappel-orange transition-colors text-sm">
                      Dakrenovatie Nieuwegein
                    </Link>
                  </li>
                </ul>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="font-bold text-lg mb-4">Contact</h3>
            <ul className="space-y-3">
              <li className="flex items-start">
                <MapPin className="mr-2 h-5 w-5 text-tappel-orange flex-shrink-0" />
                <div>
                  <span className="text-gray-600">Trondheimpad 13</span>
                  <br />
                  <span className="text-gray-600">3067DM Rotterdam</span>
                </div>
              </li>
              <li className="flex items-start">
                <Phone className="mr-2 h-5 w-5 text-tappel-orange flex-shrink-0" />
                <span className="text-gray-600">06-21452819</span>
              </li>
              <li className="flex items-start">
                <Mail className="mr-2 h-5 w-5 text-tappel-orange flex-shrink-0" />
                <span className="text-gray-600"><EMAIL></span>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-200 mt-12 pt-8 text-center text-gray-500 text-sm">
          <p>&copy; {new Date().getFullYear()} Tappel Dakwerken. KvK: 63628627. Alle rechten voorbehouden.</p>
        </div>
      </div>
    </footer>
  )
}

