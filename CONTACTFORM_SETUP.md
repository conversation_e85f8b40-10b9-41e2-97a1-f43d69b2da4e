# Contactformulier Setup

Het contactformulier gebruikt **Resend** voor het verzenden van emails. Resend is gratis tot 3000 emails per maand en werkt perfect met Vercel.

## Setup Instructies

### 1. Resend Account Aanmaken
1. Ga naar [resend.com](https://resend.com)
2. Maak een gratis account aan
3. Verifieer uw email adres

### 2. API Key Verkrijgen
1. Log in op uw Resend dashboard
2. Ga naar "API Keys" in het menu
3. K<PERSON> op "Create API Key"
4. Geef de key een naam (bijv. "Tappel Dakwerken Website")
5. <PERSON><PERSON><PERSON> de gegenereerde API key

### 3. <PERSON><PERSON> (Optioneel maar Aanbevolen)
1. Ga naar "Domains" in het Resend dashboard
2. Voeg uw domein toe (bijv. tappeldakwerken.nl)
3. Configureer de DNS records zoals aangegeven
4. Wacht tot verificatie compleet is

### 4. Environment Variabelen Instellen

#### Lokale Development
1. Kopieer `.env.example` naar `.env.local`
2. Vul de RESEND_API_KEY in:
```
RESEND_API_KEY=re_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
```

#### Vercel Deployment
1. Ga naar uw Vercel project dashboard
2. Ga naar "Settings" > "Environment Variables"
3. Voeg toe:
   - Name: `RESEND_API_KEY`
   - Value: `re_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`
   - Environment: Production (en Preview indien gewenst)

### 5. Email Adressen Aanpassen
In `app/api/contact/route.ts` kunt u de email adressen aanpassen:

```typescript
// Email naar bedrijf
to: ['<EMAIL>'],

// Van adres (moet uw geverifieerde domein gebruiken)
from: 'Contactformulier <<EMAIL>>',
```

## Functies

### Voor Bezoekers
- ✅ Gebruiksvriendelijk formulier
- ✅ Realtime validatie
- ✅ Loading states
- ✅ Success/error berichten
- ✅ Automatische bevestigingsmail

### Voor Tappel Dakwerken
- ✅ Gestructureerde email notificaties
- ✅ Alle contactgegevens overzichtelijk
- ✅ Timestamp van verzending
- ✅ Professionele opmaak

## Kosten
- **Gratis**: Tot 3000 emails per maand
- **Betaald**: $20/maand voor 50.000 emails
- Perfecte oplossing voor kleine tot middelgrote bedrijven

## Troubleshooting

### Emails komen niet aan
1. Controleer of RESEND_API_KEY correct is ingesteld
2. Controleer spam folder
3. Verifieer domein in Resend dashboard

### Formulier geeft error
1. Check browser console voor JavaScript errors
2. Controleer Vercel function logs
3. Verifieer API key permissions in Resend

### Development Testing
Voor lokale testing kunt u tijdelijk uw eigen email adres gebruiken in de `to` field om te testen of emails aankomen.
