import type React from "react"
import type { Metada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import Header from "@/components/header"
import Footer from "@/components/footer"
import SchemaOrg from "@/components/schema-org"
import ReCaptcha from "@/components/recaptcha"
import { ScrollProgress } from "@/components/motion/scroll-progress"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Tappel Dakwerken | Wij maken het zichtbaar beter - Specialist in duurzame dakrenovaties",
  description:
    "Tappel Dakwerken maakt het zichtbaar beter. Specialist in duurzame dakrenovaties voor platte en licht hellende daken. Ook met bestaande zonnepanelen installaties. Éen aanspreekpunt, zichtbaar resultaat.",
  keywords:
    "duurzame dakrenovatie, dakinspectie, lekkage herstel, platte daken, licht hellende daken, hoogwa<PERSON>ige materialen, isolatie advies, overlagen, ver<PERSON><PERSON><PERSON><PERSON><PERSON> voorko<PERSON>, gron<PERSON><PERSON> inspectie, dakspecialist, tradition<PERSON> v<PERSON>",
  authors: [{ name: "Tappel Dakwerken" }],
  creator: "Tappel Dakwerken",
  publisher: "Tappel Dakwerken",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("https://tappeldakwerken.nl"),
  alternates: {
    canonical: "/",
  },
  openGraph: {
    type: "website",
    locale: "nl_NL",
    url: "https://tappeldakwerken.nl",
    title: "Tappel Dakwerken | Wij maken het zichtbaar beter - Specialist in duurzame dakrenovaties",
    description:
      "Tappel Dakwerken maakt het zichtbaar beter. Specialist in duurzame dakrenovaties voor platte en licht hellende daken. Ook met bestaande zonnepanelen installaties. Éen aanspreekpunt, zichtbaar resultaat.",
    siteName: "Tappel Dakwerken",
    images: [
      {
        url: "https://tappeldakwerken.nl/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Tappel Dakwerken - Wij maken het zichtbaar beter - Duurzame dakrenovaties",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Tappel Dakwerken | Wij maken het zichtbaar beter - Specialist in duurzame dakrenovaties",
    description:
      "Tappel Dakwerken maakt het zichtbaar beter. Specialist in duurzame dakrenovaties voor platte en licht hellende daken. Ook met bestaande zonnepanelen installaties. Éen aanspreekpunt, zichtbaar resultaat.",
    images: ["https://tappeldakwerken.nl/og-image.jpg"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="nl">
      <body className={inter.className}>
        <SchemaOrg />
        <ReCaptcha />
        <ScrollProgress color="#FF6B35" height={3} />
        <Header />
        <main className="min-h-screen">{children}</main>
        <Footer />
        <script
          type="text/javascript"
          dangerouslySetInnerHTML={{
            __html: `
              (function(c,l,a,r,i,t,y){
                  c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                  t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                  y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
              })(window, document, "clarity", "script", "sur6539iuj");
            `,
          }}
        />
      </body>
    </html>
  )
}

