import Image from "next/image"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Leaf, ThumbsUp, Award } from "lucide-react"
import { AnimatedText } from "@/components/motion/animated-text"
import { AnimatedButton } from "@/components/motion/animated-button"
import { AnimatedCard } from "@/components/motion/animated-card"
import Testimonials from "@/components/testimonials"
import type { Metadata } from "next"

export const metadata: Metadata = {
  title: "Over Ons | Tappel Dakwerken - Specialist in Duurzame Dakoplossingen",
  description:
    "<PERSON><PERSON> ken<PERSON> met <PERSON><PERSON>kwerken, uw specialist in duurzame dakoplossingen. Ontdek onze missie, visie en het team achter onze hoogwaardige dakdiensten.",
  keywords: "Ta<PERSON> Dakwerken, dakspecialist, over ons, dakbedrijf, duurzame dakoplossingen, dakdoorvoeren, zonnepanelen",
  alternates: {
    canonical: "/over-ons",
  },
  openGraph: {
    title: "Over Ons | Tappel Dakwerken - Specialist in Duurzame Dakoplossingen",
    description:
      "<PERSON><PERSON> ken<PERSON> met <PERSON><PERSON>, uw specialist in duurzame dakoplossingen. Ont<PERSON> onze missie, visie en het team achter onze hoogwaardige dakdiensten.",
    url: "https://tappeldakwerken.nl/over-ons",
    siteName: "Tappel Dakwerken",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Tappel Dakwerken - Specialist in Duurzame Dakoplossingen",
      },
    ],
    locale: "nl_NL",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Over Ons | Tappel Dakwerken - Specialist in Duurzame Dakoplossingen",
    description:
      "Maak kennis met Tappel Dakwerken, uw specialist in duurzame dakoplossingen. Ontdek onze missie, visie en het team achter onze hoogwaardige dakdiensten.",
    images: ["/og-image.jpg"],
  },
  robots: {
    index: true,
    follow: true,
    "max-video-preview": -1,
    "max-image-preview": "large",
    "max-snippet": -1,
  },
  category: "Over Ons",
  classification: "Bedrijfsinformatie",
}

export default function OverOnsPage() {
  return (
    <>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-gray-50 to-white py-12 md:py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <AnimatedText variant="fade-up">
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                Over <span className="text-tappel-orange">Tappel Dakwerken</span>
              </h1>
            </AnimatedText>
            <AnimatedText variant="fade-up" delay={0.2}>
              <p className="text-xl text-gray-700">
                Uw specialist in duurzame dakoplossingen en dakdoorvoeren voor zonnepanelen.
              </p>
            </AnimatedText>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-16 md:py-24">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 gap-12 items-center mb-16">
            <AnimatedText variant="fade-up" delay={0.3}>
              
                <AnimatedCard variant="hover-lift">
                  <Image
                    src="/placeholder.svg?height=500&width=600"
                    alt="Het professionele team van Tappel Dakwerken dakspecialisten"
                    width={600}
                    height={500}
                    className="rounded-2xl shadow-lg object-cover"
                  />
                </AnimatedCard>
              
            </AnimatedText>
            <div>
              <AnimatedText variant="fade-up" delay={0.4}>
                <h2 className="text-3xl font-bold mb-6">Wie Wij Zijn</h2>
              </AnimatedText>
              <AnimatedText variant="fade-up" delay={0.5}>
                <p className="text-lg text-gray-700 mb-4">
                  Tappel Dakwerken is een gespecialiseerd dakbedrijf met jarenlange ervaring in de verduurzaming en renovatie van
                  platte en licht hellende daken. Onze focus ligt op het leveren van hoogwaardige dakdoorvoeren en
                  montagesystemen voor zonnepanelen.
                </p>
              </AnimatedText>
              <AnimatedText variant="fade-up" delay={0.6}>
                <p className="text-lg text-gray-700 mb-6">
                  Gevestigd in Rotterdam (Trondheimpad 13, 3067DM), combineren wij vakmanschap met innovatie om duurzame
                  en betrouwbare dakoplossingen te bieden die perfect aansluiten bij de behoeften van onze klanten. Of het
                  nu gaat om een eenvoudige dakdoorvoer of een complete dakrenovatie, wij staan garant voor kwaliteit en
                  professionaliteit.
                </p>
              </AnimatedText>
              <AnimatedText variant="fade-up" delay={0.7}>
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center gap-2">
                    <Users className="h-5 w-5 text-tappel-orange" />
                    <span className="font-medium">Ervaren team</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Leaf className="h-5 w-5 text-tappel-orange" />
                    <span className="font-medium">Duurzame oplossingen</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <ThumbsUp className="h-5 w-5 text-tappel-orange" />
                    <span className="font-medium">Klanttevredenheid</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Award className="h-5 w-5 text-tappel-orange" />
                    <span className="font-medium">Gecertificeerd</span>
                  </div>
                </div>
              </AnimatedText>
            </div>
          </div>

          {/* Mission & Vision */}
          <div className="grid md:grid-cols-2 gap-12 mb-16">
            <AnimatedText variant="fade-up" delay={0.8}>
              
                <AnimatedCard variant="hover-lift" className="bg-white p-8 rounded-2xl shadow-lg border border-gray-100">
                  <h3 className="text-2xl font-bold mb-4 text-tappel-orange">Onze Missie</h3>
                  <p className="text-gray-700">
                    Onze missie is om hoogwaardige en duurzame dakoplossingen te bieden die bijdragen aan de
                    energietransitie. Wij streven ernaar om elke klant te voorzien van een betrouwbaar en duurzaam dak dat
                    optimaal is voorbereid voor zonnepanelen en andere duurzame toepassingen.
                  </p>
                </AnimatedCard>
              
            </AnimatedText>
            <AnimatedText variant="fade-up" delay={0.9}>
              
                <AnimatedCard variant="hover-lift" className="bg-white p-8 rounded-2xl shadow-lg border border-gray-100">
                  <h3 className="text-2xl font-bold mb-4 text-tappel-orange">Onze Visie</h3>
                  <p className="text-gray-700">
                    Wij geloven in een toekomst waarin elk dak bijdraagt aan een duurzame samenleving. Door innovatieve
                    dakoplossingen te ontwikkelen en toe te passen, willen wij een leidende rol spelen in de verduurzaming
                    van de gebouwde omgeving en de transitie naar schone energie.
                  </p>
                </AnimatedCard>
              
            </AnimatedText>
          </div>

          {/* Why Choose Us */}
          <div className="mb-16">
            <AnimatedText variant="fade-up" delay={1.0}>
              <h2 className="text-3xl font-bold mb-8 text-center">Waarom Kiezen voor <span className="text-tappel-orange">Tappel Dakwerken</span>?</h2>
            </AnimatedText>
            <div className="grid md:grid-cols-3 gap-8">
              <AnimatedText variant="fade-up" delay={1.1}>
                
                  <AnimatedCard variant="hover-lift" className="bg-white p-6 rounded-2xl shadow-lg border border-gray-100 h-full">
                    <div className="w-12 h-12 bg-tappel-orange rounded-full flex items-center justify-center mb-4">
                      <Users className="h-6 w-6 text-white" />
                    </div>
                    <h3 className="text-xl font-bold mb-3">Expertise</h3>
                    <p className="text-gray-600">
                      Ons team bestaat uit ervaren dakspecialisten met diepgaande kennis van verschillende daktypen en
                      -systemen. Wij weten precies wat nodig is voor een optimale installatie van dakdoorvoeren en
                      zonnepanelen.
                    </p>
                  </AnimatedCard>
                
              </AnimatedText>

              <AnimatedText variant="fade-up" delay={1.2}>
                
                  <AnimatedCard variant="hover-lift" className="bg-white p-6 rounded-2xl shadow-lg border border-gray-100 h-full">
                    <div className="w-12 h-12 bg-tappel-orange rounded-full flex items-center justify-center mb-4">
                      <Leaf className="h-6 w-6 text-white" />
                    </div>
                    <h3 className="text-xl font-bold mb-3">Duurzaamheid</h3>
                    <p className="text-gray-600">
                      Duurzaamheid staat centraal in alles wat we doen. We gebruiken milieuvriendelijke materialen en
                      technieken om de ecologische voetafdruk van onze werkzaamheden te minimaliseren.
                    </p>
                  </AnimatedCard>
                
              </AnimatedText>

              <AnimatedText variant="fade-up" delay={1.3}>
                
                  <AnimatedCard variant="hover-lift" className="bg-white p-6 rounded-2xl shadow-lg border border-gray-100 h-full">
                    <div className="w-12 h-12 bg-tappel-orange rounded-full flex items-center justify-center mb-4">
                      <ThumbsUp className="h-6 w-6 text-white" />
                    </div>
                    <h3 className="text-xl font-bold mb-3">Kwaliteit</h3>
                    <p className="text-gray-600">
                      Wij staan voor kwaliteit en vakmanschap. Elk project wordt met de grootste zorg en aandacht
                      uitgevoerd, waarbij we alleen werken met hoogwaardige materialen en beproefde technieken.
                    </p>
                  </AnimatedCard>
                
              </AnimatedText>
            </div>
          </div>

          {/* Team */}
          <div>
            <AnimatedText variant="fade-up" delay={1.4}>
              <h2 className="text-3xl font-bold mb-8 text-center">Ons <span className="text-tappel-orange">Team</span></h2>
            </AnimatedText>
            <div className="grid md:grid-cols-3 gap-8">
              <AnimatedText variant="fade-up" delay={1.5}>
                
                  <AnimatedCard variant="hover-lift" className="text-center">
                    <div className="relative w-48 h-48 mx-auto mb-4 overflow-hidden rounded-full">
                      <Image
                        src="/placeholder.svg?height=200&width=200"
                        alt="Jan Jansen - Oprichter & Dakspecialist bij Tappel Dakwerken"
                        fill
                        className="object-cover"
                      />
                    </div>
                    <h3 className="text-xl font-bold">Jan Jansen</h3>
                    <p className="text-gray-600">Oprichter & Dakspecialist</p>
                  </AnimatedCard>
                
              </AnimatedText>

              <AnimatedText variant="fade-up" delay={1.6}>
                
                  <AnimatedCard variant="hover-lift" className="text-center">
                    <div className="relative w-48 h-48 mx-auto mb-4 overflow-hidden rounded-full">
                      <Image
                        src="/placeholder.svg?height=200&width=200"
                        alt="Petra de Vries - Projectmanager bij Tappel Dakwerken"
                        fill
                        className="object-cover"
                      />
                    </div>
                    <h3 className="text-xl font-bold">Petra de Vries</h3>
                    <p className="text-gray-600">Projectmanager</p>
                  </AnimatedCard>
                
              </AnimatedText>

              <AnimatedText variant="fade-up" delay={1.7}>
                
                  <AnimatedCard variant="hover-lift" className="text-center">
                    <div className="relative w-48 h-48 mx-auto mb-4 overflow-hidden rounded-full">
                      <Image
                        src="/placeholder.svg?height=200&width=200"
                        alt="Mark Bakker - Technisch Specialist bij Tappel Dakwerken"
                        fill
                        className="object-cover"
                      />
                    </div>
                    <h3 className="text-xl font-bold">Mark Bakker</h3>
                    <p className="text-gray-600">Technisch Specialist</p>
                  </AnimatedCard>
                
              </AnimatedText>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 md:py-24 bg-gradient-to-r from-tappel-orange to-orange-400 text-white">
        <div className="container mx-auto px-4 text-center">
          <AnimatedText variant="fade-up" delay={1.8}>
            <h2 className="text-3xl md:text-4xl font-bold mb-6">Klaar om samen te werken?</h2>
          </AnimatedText>
          <AnimatedText variant="fade-up" delay={1.9}>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Neem contact met ons op voor een vrijblijvend gesprek over uw dakproject. Wij denken graag met u mee over de
              beste oplossing voor uw situatie.
            </p>
          </AnimatedText>
          <AnimatedText variant="fade-up" delay={2.0}>
            <AnimatedButton 
              asChild 
              size="lg"
              variant="secondary"
              className="bg-white text-tappel-orange hover:bg-gray-100 font-bold"
              glow
            >
              <Link href="/contact">NEEM CONTACT OP</Link>
            </AnimatedButton>
          </AnimatedText>
        </div>
      </section>

      {/* Testimonials */}
      <Testimonials />
    </>
  )
}
