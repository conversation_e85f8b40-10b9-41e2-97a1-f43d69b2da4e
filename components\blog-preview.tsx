"use client"

import Link from "next/link"
import { blogPosts } from "@/lib/blog-data"
import { CardStack } from "@/components/motion/card-stack"
import { AnimatedText } from "@/components/motion/animated-text"
import { AnimatedButton } from "@/components/motion/animated-button"


export default function BlogPreview() {

  return (
    <section className="py-16 md:py-24 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center mb-12">
            <div>
              <AnimatedText variant="fade-up">
                <h2 className="text-3xl md:text-4xl font-bold mb-6">Onze Blog</h2>
              </AnimatedText>
              <AnimatedText variant="fade-up" delay={0.2}>
                <p className="text-lg text-gray-700 mb-8">
                  Professionele tips, advies en informatie over dak<PERSON><PERSON><PERSON>eren, zonnepanelen,
                  dakrenovaties en het onderhouden van uw dak. Hover over de kaarten voor meer details.
                </p>
              </AnimatedText>
              <AnimatedText variant="fade-up" delay={0.4}>
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-tappel-orange rounded-full"></div>
                    <span className="text-gray-700">Praktische daktips</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-gray-700">Zonnepaneel advies</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <span className="text-gray-700">Onderhoudstips</span>
                  </div>
                </div>
              </AnimatedText>

              <AnimatedText variant="fade-up" delay={0.6}>
                <div className="mt-8">
                  <AnimatedButton asChild variant="primary" className="w-full sm:w-auto">
                    <Link href="/blog">ALLE ARTIKELEN</Link>
                  </AnimatedButton>
                </div>
              </AnimatedText>
            </div>

            <div className="flex justify-center">
              <CardStack
                items={blogPosts.slice(0, 5).map(post => ({
                  id: post.slug,
                  title: post.title,
                  description: post.excerpt,
                  image: post.coverImage,
                  tags: post.categories.slice(0, 2),
                  year: new Date(post.publishedAt).getFullYear().toString(),
                  href: `/blog/${post.slug}`
                }))}
                maxVisible={5}
                stackOffset={12}
                scaleStep={0.03}
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

