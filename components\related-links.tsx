import Link from "next/link";

interface RelatedLinksProps {
  currentCity: string;
}

export default function RelatedLinks({ currentCity }: RelatedLinksProps) {
  const getRelatedCities = (city: string) => {
    const allCities = [
      { name: "Rotterdam", slug: "rotterdam", region: "Zuid-Holland" },
      { name: "Den Haag", slug: "den-haag", region: "Zuid-Holland" },
      { name: "Amsterdam", slug: "amsterdam", region: "Noord-Holland" },
      { name: "Utrecht", slug: "utrecht", region: "Utrecht" },
      { name: "Haarlem", slug: "haarlem", region: "Noord-Holland" },
      { name: "Leiden", slug: "leiden", region: "Zuid-Holland" },
      { name: "Delft", slug: "delft", region: "Zuid-Holland" },
      { name: "Zoetermeer", slug: "zoetermeer", region: "Zuid-Holland" },
      { name: "Alkmaar", slug: "alkmaar", region: "Noord-Holland" },
      { name: "Gouda", slug: "gouda", region: "Zuid-Holland" }
    ];

    return allCities.filter(c => c.name !== city).slice(0, 5);
  };

  const getRelatedServices = () => [
    { name: "Dakdoorvoeren", slug: "dakdoorvoer", description: "Waterdichte doorvoeren voor installaties" },
    { name: "Dakinspectie", slug: "dakinspectie", description: "Professionele dakinspectie en rapport" },
    { name: "Dakisolatie", slug: "dakisolatie", description: "Verbeterde isolatie voor energiebesparing" },
    { name: "Lekkage Herstel", slug: "lekkage-herstel", description: "Spoedservice voor daklekkages" },
    { name: "Groendaken", slug: "groendak", description: "Duurzame groendak installaties" }
  ];

  const relatedCities = getRelatedCities(currentCity);
  const relatedServices = getRelatedServices();

  return (
    <section className="py-16 md:py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Gerelateerde Diensten & Locaties
            </h2>
            <p className="text-lg text-gray-700 max-w-3xl mx-auto">
              Ontdek onze andere diensten en locaties in Nederland
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12">
            {/* Related Services */}
            <div>
              <h3 className="text-2xl font-bold mb-6">Andere Diensten</h3>
              <div className="space-y-4">
                {relatedServices.map((service, index) => (
                  <Link 
                    key={index}
                    href={`/diensten/${service.slug}`} 
                    className="block bg-white p-4 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group"
                  >
                    <h4 className="text-lg font-bold text-gray-900 group-hover:text-tappel-orange transition-colors mb-2">
                      {service.name} {currentCity}
                    </h4>
                    <p className="text-gray-600 text-sm mb-2">
                      {service.description}
                    </p>
                    <div className="text-tappel-orange font-medium text-sm">
                      Meer info →
                    </div>
                  </Link>
                ))}
              </div>
            </div>

            {/* Related Cities */}
            <div>
              <h3 className="text-2xl font-bold mb-6">Andere Locaties</h3>
              <div className="space-y-4">
                {relatedCities.map((city, index) => (
                  <Link 
                    key={index}
                    href={`/dakrenovatie-${city.slug}`} 
                    className="block bg-white p-4 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group"
                  >
                    <h4 className="text-lg font-bold text-gray-900 group-hover:text-tappel-orange transition-colors mb-2">
                      Dakrenovatie {city.name}
                    </h4>
                    <p className="text-gray-600 text-sm mb-2">
                      Specialist in dakrenovaties in {city.region}
                    </p>
                    <div className="text-tappel-orange font-medium text-sm">
                      Bekijk diensten →
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          </div>

          {/* CTA Section */}
          <div className="text-center mt-12">
            <div className="bg-tappel-blue text-white p-8 rounded-lg">
              <h3 className="text-2xl font-bold mb-4">
                Niet uw locatie gevonden?
              </h3>
              <p className="text-lg mb-6">
                Wij werken in heel Nederland. Neem contact op voor een vrijblijvende offerte.
              </p>
              <Link 
                href="/contact" 
                className="inline-block bg-tappel-orange text-white px-8 py-3 rounded-lg font-semibold hover:bg-orange-600 transition-colors"
              >
                Contact Opnemen
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
